"use strict";
// ==UserScript==
// @name         Agario Enhanced Script
// @namespace    agario-enhanced
// @version      2.0.0
// <AUTHOR>
// @description  Script avançado para Agario com interceptação WebSocket e gerenciamento de estado
// @license      MIT
// @match        https://agariobr.com.br/*
// @grant        none
// @run-at       document-start
// ==/UserScript==
// ===== CONFIGURAÇÕES =====
const CONFIG = {
    SCRIPT_NAME: "agario-enhanced",
    VERSION: "2.0.0",
    STORAGE_KEYS: {
        SETTINGS: "agario_settings",
        WORLD_STATE: "agario_world_state",
        NETWORK_STATS: "agario_network_stats",
    },
    NETWORK: {
        WEBSOCKET_URL: "servers.agariobr.com.br:4409",
        PING_INTERVAL: 2000,
        RECONNECT_DELAY: 1000,
        MAX_RETRIES: 5,
    },
    DEFAULT_SETTINGS: {
        cameraMovement: "default",
        camera: "default",
        multibox: true,
        nbox: false,
        mergeCamera: true,
        cameraSpawnAnimation: true,
        cameraSmoothness: 10,
        autoZoom: true,
        synchronization: "flawless",
        drawDelay: 120,
        slowerJellyPhysics: false,
        enableDebugMode: false,
    },
};
// ===== ENUMS E TIPOS DE REDE =====
var NetworkOpcodeEnum;
(function (NetworkOpcodeEnum) {
    NetworkOpcodeEnum[NetworkOpcodeEnum["WorldUpdate"] = 16] = "WorldUpdate";
    NetworkOpcodeEnum[NetworkOpcodeEnum["PositionUpdate"] = 17] = "PositionUpdate";
    NetworkOpcodeEnum[NetworkOpcodeEnum["Leaderboard"] = 18] = "Leaderboard";
    NetworkOpcodeEnum[NetworkOpcodeEnum["ClearCells"] = 20] = "ClearCells";
    NetworkOpcodeEnum[NetworkOpcodeEnum["RemoveCells"] = 32] = "RemoveCells";
    NetworkOpcodeEnum[NetworkOpcodeEnum["MapBorders"] = 48] = "MapBorders";
    NetworkOpcodeEnum[NetworkOpcodeEnum["OwnCells"] = 64] = "OwnCells";
    NetworkOpcodeEnum[NetworkOpcodeEnum["ChatMessage"] = 99] = "ChatMessage";
    NetworkOpcodeEnum[NetworkOpcodeEnum["ServerStats"] = 100] = "ServerStats";
    NetworkOpcodeEnum[NetworkOpcodeEnum["PartyInfo"] = 101] = "PartyInfo";
    NetworkOpcodeEnum[NetworkOpcodeEnum["PartyUpdate"] = 102] = "PartyUpdate";
    NetworkOpcodeEnum[NetworkOpcodeEnum["Minimap"] = 103] = "Minimap";
    NetworkOpcodeEnum[NetworkOpcodeEnum["PasswordError"] = 180] = "PasswordError";
})(NetworkOpcodeEnum || (NetworkOpcodeEnum = {}));
var ConnectionState;
(function (ConnectionState) {
    ConnectionState["DISCONNECTED"] = "disconnected";
    ConnectionState["CONNECTING"] = "connecting";
    ConnectionState["CONNECTED"] = "connected";
    ConnectionState["RECONNECTING"] = "reconnecting";
    ConnectionState["ERROR"] = "error";
})(ConnectionState || (ConnectionState = {}));
var EventType;
(function (EventType) {
    EventType["WORLD_UPDATE"] = "world_update";
    EventType["POSITION_UPDATE"] = "position_update";
    EventType["CELL_SPAWN"] = "cell_spawn";
    EventType["CELL_DEATH"] = "cell_death";
    EventType["CONNECTION_STATE_CHANGE"] = "connection_state_change";
    EventType["LEADERBOARD_UPDATE"] = "leaderboard_update";
    EventType["CHAT_MESSAGE"] = "chat_message";
    EventType["ERROR"] = "error";
})(EventType || (EventType = {}));
class EventEmitter {
    constructor() {
        this.listeners = new Map();
    }
    on(eventType, listener) {
        if (!this.listeners.has(eventType)) {
            this.listeners.set(eventType, new Set());
        }
        this.listeners.get(eventType).add(listener);
    }
    off(eventType, listener) {
        const eventListeners = this.listeners.get(eventType);
        if (eventListeners) {
            eventListeners.delete(listener);
        }
    }
    emit(event) {
        const eventListeners = this.listeners.get(event.type);
        if (eventListeners) {
            eventListeners.forEach(listener => {
                try {
                    listener(event);
                }
                catch (error) {
                    console.error(`Erro ao executar listener para evento ${event.type}:`, error);
                }
            });
        }
    }
    removeAllListeners(eventType) {
        if (eventType) {
            this.listeners.delete(eventType);
        }
        else {
            this.listeners.clear();
        }
    }
}
class StorageService {
    constructor() { }
    static getInstance() {
        if (!StorageService.instance) {
            StorageService.instance = new StorageService();
        }
        return StorageService.instance;
    }
    get(key) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : null;
        }
        catch (error) {
            console.error(`Erro ao recuperar item do storage: ${key}`, error);
            return null;
        }
    }
    set(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
            return true;
        }
        catch (error) {
            console.error(`Erro ao salvar item no storage: ${key}`, error);
            return false;
        }
    }
    remove(key) {
        try {
            localStorage.removeItem(key);
            return true;
        }
        catch (error) {
            console.error(`Erro ao remover item do storage: ${key}`, error);
            return false;
        }
    }
    clear() {
        try {
            localStorage.clear();
            return true;
        }
        catch (error) {
            console.error("Erro ao limpar storage", error);
            return false;
        }
    }
    exists(key) {
        return localStorage.getItem(key) !== null;
    }
}
class DOMUtilities {
    constructor() { }
    static getInstance() {
        if (!DOMUtilities.instance) {
            DOMUtilities.instance = new DOMUtilities();
        }
        return DOMUtilities.instance;
    }
    createElement(tag, options = {}) {
        const element = document.createElement(tag);
        if (options.className)
            element.className = options.className;
        if (options.textContent)
            element.textContent = options.textContent;
        if (options.styles)
            Object.assign(element.style, options.styles);
        if (options.attributes) {
            Object.entries(options.attributes).forEach(([key, value]) => {
                element.setAttribute(key, value);
            });
        }
        if (options.eventListeners) {
            Object.entries(options.eventListeners).forEach(([event, listener]) => {
                element.addEventListener(event, listener);
            });
        }
        return element;
    }
    removeElement(element) {
        if (element === null || element === void 0 ? void 0 : element.parentNode) {
            element.parentNode.removeChild(element);
            return true;
        }
        return false;
    }
    findElement(selector) {
        return document.querySelector(selector);
    }
    findElements(selector) {
        return Array.from(document.querySelectorAll(selector));
    }
}
class SettingsStore {
    constructor() {
        this.settings = {};
        this.storageKey = CONFIG.STORAGE_KEYS.SETTINGS;
        this.storageService = StorageService.getInstance();
        this.loadSettings();
    }
    static getInstance() {
        if (!SettingsStore.instance) {
            SettingsStore.instance = new SettingsStore();
        }
        return SettingsStore.instance;
    }
    loadSettings() {
        const savedSettings = this.storageService.get(this.storageKey);
        if (savedSettings && typeof savedSettings === "object") {
            this.settings = Object.assign(Object.assign({}, CONFIG.DEFAULT_SETTINGS), savedSettings);
        }
        else {
            this.settings = Object.assign({}, CONFIG.DEFAULT_SETTINGS);
        }
    }
    saveSettings() {
        return this.storageService.set(this.storageKey, this.settings);
    }
    getAllSettings() {
        return Object.assign({}, this.settings);
    }
    getSetting(key) {
        return this.settings[key] || null;
    }
    setSetting(key, value) {
        this.settings[key] = value;
        return this.saveSettings();
    }
    resetToDefaults() {
        this.settings = Object.assign({}, CONFIG.DEFAULT_SETTINGS);
        return this.saveSettings();
    }
}
class KeyBindManager {
    constructor() {
        this.keyBindings = new Map();
        this.setupGlobalListener();
    }
    static getInstance() {
        if (!KeyBindManager.instance) {
            KeyBindManager.instance = new KeyBindManager();
        }
        return KeyBindManager.instance;
    }
    setupGlobalListener() {
        document.addEventListener("keydown", event => {
            const key = event.key.toLowerCase();
            const binding = this.keyBindings.get(key);
            if (binding) {
                const result = binding.handler(event);
                if (result !== false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
            }
        });
    }
    register(binding) {
        const key = binding.key.toLowerCase();
        this.keyBindings.set(key, binding);
        return true;
    }
    listBindings() {
        return Array.from(this.keyBindings.values());
    }
}
class WebSocketInterceptor extends EventEmitter {
    constructor() {
        super();
        this.textEncoder = new TextEncoder();
        this.startTime = performance.now();
        // Gerenciamento do estado da conexão
        this.connections = new Map();
        this.messageLog = [];
        this.connectionStates = new Map();
        this.originalWebSocket = window.WebSocket;
        this.setupWebSocketInterception();
        this.startPeriodicTasks();
    }
    // ===== PADRÃO SINGLETON =====
    static getInstance() {
        if (!WebSocketInterceptor.instance) {
            WebSocketInterceptor.instance = new WebSocketInterceptor();
        }
        return WebSocketInterceptor.instance;
    }
    // ===== CONFIGURAÇÃO DA INTERCEPTAÇÃO DO WEBSOCKET =====
    /** Intercepta o construtor do WebSocket para monitorar conexões com o servidor alvo */
    setupWebSocketInterception() {
        const self = this;
        window.WebSocket = class extends self.originalWebSocket {
            constructor(url, protocols) {
                super(url, protocols);
                const urlString = url.toString();
                if (urlString.includes(CONFIG.NETWORK.WEBSOCKET_URL)) {
                    self.handleNewConnection(this, urlString);
                }
            }
        };
        // Preserva a cadeia de protótipos original do WebSocket
        Object.setPrototypeOf(window.WebSocket.prototype, this.originalWebSocket.prototype);
        Object.setPrototypeOf(window.WebSocket, this.originalWebSocket);
    }
    /** Lida com a detecção de uma nova conexão WebSocket */
    handleNewConnection(socket, url) {
        console.log("🔗 Nova conexão WebSocket detectada:", url);
        const viewId = this.findOrCreateViewId();
        const connection = this.getOrCreateConnection(viewId);
        connection.websocket = socket;
        this.attachEventListeners(socket, viewId);
    }
    // ===== GERENCIAMENTO DE CONEXÃO =====
    /** Obtém ou cria uma conexão para a visualização especificada */
    getOrCreateConnection(viewId) {
        if (this.connections.has(viewId)) {
            return this.connections.get(viewId);
        }
        const connection = {
            handshake: undefined,
            websocket: undefined,
            latency: undefined,
            lastPingTime: undefined,
            playBlock: undefined,
            rejectionCount: 0,
            retryCount: 3,
        };
        this.connections.set(viewId, connection);
        return connection;
    }
    /** Apenas intercepta conexões existentes - não cria novas */
    createConnection(viewId) {
        // Não criamos novas conexões - apenas interceptamos as existentes
        console.log("🔍 Interceptando conexões existentes apenas - não criando novas");
        return undefined;
    }
    // ===== OUVINTES DE EVENTOS =====
    /** Anexa ouvintes de eventos à instância do WebSocket */
    attachEventListeners(socket, viewId) {
        const originalSend = socket.send.bind(socket);
        // Intercepta mensagens enviadas
        socket.send = (data) => {
            this.logMessage(viewId, data, "sent");
            return originalSend(data);
        };
        // Anexa manipuladores de eventos do WebSocket
        socket.addEventListener("open", () => this.handleConnectionOpen(socket, viewId));
        socket.addEventListener("message", event => this.handleMessage(event, viewId));
        socket.addEventListener("error", event => this.handleConnectionError(event, viewId));
        socket.addEventListener("close", event => this.handleConnectionClose(event, viewId));
    }
    // ===== MANIPULADORES DE EVENTOS =====
    /** Lida com o evento de abertura da conexão WebSocket */
    handleConnectionOpen(socket, viewId) {
        console.log("✅ WebSocket conectado:", socket.url);
        const connection = this.connections.get(viewId);
        if (!connection) {
            socket.close();
            return;
        }
        // Reseta contadores de erro em conexão bem-sucedida
        connection.rejectionCount = 0;
        connection.retryCount = 3;
        // Envia handshake inicial
        socket.send(this.textEncoder.encode(""));
    }
    /** Lida com mensagens recebidas do WebSocket */
    handleMessage(event, viewId) {
        const connection = this.connections.get(viewId);
        if (!connection)
            return;
        const dataView = new DataView(event.data);
        this.logMessage(viewId, event.data, "received");
        // Processa handshake se ainda não estabelecido
        if (!connection.handshake) {
            this.processHandshake(dataView, connection);
            return;
        }
        // Decodifica o opcode da mensagem e processa
        const rawOpcode = dataView.getUint8(0);
        const decodedOpcode = connection.handshake.decryptTable[rawOpcode];
        dataView.setUint8(0, decodedOpcode);
        this.processGameMessage(dataView, decodedOpcode, viewId, connection);
    }
    /** Lida com erros de conexão WebSocket */
    handleConnectionError(event, viewId) {
        console.error("💥 Erro no WebSocket:", event);
        this.updateConnectionState(viewId, ConnectionState.ERROR, "WebSocket error occurred");
    }
    /** Lida com o fechamento da conexão WebSocket */
    handleConnectionClose(event, viewId) {
        console.log("🔌 WebSocket fechado:", event.code, event.reason);
        const connection = this.connections.get(viewId);
        if (!connection)
            return;
        this.resetConnectionState(connection);
        this.attemptReconnection(viewId, connection);
    }
    // ===== PROCESSAMENTO DE MENSAGENS =====
    /** Processa a mensagem inicial de handshake para estabelecer tabelas de criptografia */
    processHandshake(dataView, connection) {
        const HANDSHAKE_OFFSET = 10;
        const TABLE_SIZE = 256;
        const encryptTable = new Uint8Array(TABLE_SIZE);
        const decryptTable = new Uint8Array(TABLE_SIZE);
        // Constrói tabelas de lookup de criptografia/descriptografia
        for (let i = 0; i < TABLE_SIZE; i++) {
            const encryptedValue = dataView.getUint8(HANDSHAKE_OFFSET + i);
            encryptTable[i] = encryptedValue;
            decryptTable[encryptedValue] = i;
        }
        connection.handshake = { encryptTable, decryptTable };
        console.log("🤝 Handshake estabelecido com sucesso");
    }
    /** Direciona mensagens do jogo decodificadas com base no opcode */
    processGameMessage(dataView, opcode, viewId, connection) {
        const timestamp = performance.now();
        switch (opcode) {
            case NetworkOpcodeEnum.WorldUpdate:
                this.processWorldUpdate(dataView, viewId, timestamp);
                break;
            case NetworkOpcodeEnum.PositionUpdate:
                this.processPositionUpdate(dataView, viewId);
                break;
            case NetworkOpcodeEnum.Leaderboard:
                this.processLeaderboard(dataView, viewId);
                break;
            case NetworkOpcodeEnum.ClearCells:
                this.processClearCells(dataView, viewId);
                break;
            case NetworkOpcodeEnum.RemoveCells:
                this.processRemoveCells(dataView, viewId);
                break;
            case NetworkOpcodeEnum.MapBorders:
                this.processMapBorders(dataView, viewId);
                break;
            case NetworkOpcodeEnum.OwnCells:
                this.processOwnCells(dataView, viewId, timestamp);
                break;
            case NetworkOpcodeEnum.ChatMessage:
                this.processChatMessage(dataView, viewId);
                break;
            case NetworkOpcodeEnum.ServerStats:
                this.processServerStats(dataView, viewId, connection, timestamp);
                break;
            case NetworkOpcodeEnum.PasswordError:
                this.processPasswordError();
                break;
            default:
                console.log(`❓ Opcode desconhecido: 0x${opcode.toString(16)}`);
        }
    }
    // ===== PROCESSADORES DE MENSAGENS DO JOGO =====
    processWorldUpdate(dataView, viewId, timestamp) {
        try {
            const world = World.getInstance();
            const vision = world.createVision(viewId);
            const cells = new Map();
            const pellets = new Map();
            let offset = 1; // Pula o opcode
            // Lê células removidas
            const removedCellsCount = dataView.getUint16(offset, true);
            offset += 2;
            for (let i = 0; i < removedCellsCount; i++) {
                const cellId = dataView.getUint32(offset, true);
                offset += 4;
                world.removeCell(cellId, viewId, timestamp);
            }
            // Lê células atualizadas/novas
            while (offset < dataView.byteLength) {
                const cellId = dataView.getUint32(offset, true);
                offset += 4;
                if (cellId === 0)
                    break; // Fim das células
                const x = dataView.getFloat32(offset, true);
                offset += 4;
                const y = dataView.getFloat32(offset, true);
                offset += 4;
                const radius = dataView.getFloat32(offset, true);
                offset += 4;
                // Lê cor (3 bytes RGB)
                const r = dataView.getUint8(offset++);
                const g = dataView.getUint8(offset++);
                const b = dataView.getUint8(offset++);
                const color = `rgb(${r},${g},${b})`;
                // Lê flags
                const flags = dataView.getUint8(offset++);
                const isVirus = (flags & 1) !== 0;
                const isFood = (flags & 2) !== 0;
                const isEjected = (flags & 4) !== 0;
                // Lê nome se presente
                let name = "";
                if (!isFood && !isVirus) {
                    const nameLength = dataView.getUint16(offset, true);
                    offset += 2;
                    if (nameLength > 0) {
                        const nameBytes = new Uint16Array(dataView.buffer, offset, nameLength / 2);
                        name = String.fromCharCode(...nameBytes);
                        offset += nameLength;
                    }
                }
                const cellData = {
                    id: cellId,
                    position: { x, y },
                    radius,
                    mass: (radius * radius) / 100,
                    color,
                    name,
                    isOwned: vision.owned.has(cellId),
                    isAlive: true,
                    lastUpdate: timestamp,
                };
                if (isFood) {
                    pellets.set(cellId, cellData);
                    world.updatePellet(cellId, cellData, viewId, timestamp);
                }
                else {
                    cells.set(cellId, cellData);
                    world.updateCell(cellId, cellData, viewId, timestamp);
                }
            }
            // Emite evento de atualização do mundo
            this.emit({
                type: EventType.WORLD_UPDATE,
                timestamp,
                viewId,
                cells,
                pellets,
            });
            if (CONFIG.DEFAULT_SETTINGS.enableDebugMode) {
                console.log("🌍 Mundo atualizado:", {
                    cellCount: cells.size,
                    pelletCount: pellets.size,
                    removedCount: removedCellsCount,
                });
            }
        }
        catch (error) {
            console.error("Erro ao processar atualização do mundo:", error);
        }
    }
    processPositionUpdate(dataView, viewId) {
        try {
            const x = dataView.getFloat32(1, true);
            const y = dataView.getFloat32(5, true);
            const scale = dataView.getFloat32(9, true);
            const timestamp = performance.now();
            // Atualiza posição da câmera no mundo
            const world = World.getInstance();
            world.updateCameraPosition(viewId, x, y, scale, timestamp);
            this.emit({
                type: EventType.POSITION_UPDATE,
                timestamp,
                viewId,
                position: { x, y },
                scale,
            });
            if (CONFIG.DEFAULT_SETTINGS.enableDebugMode) {
                console.log(`📍 Posição da câmera: (${x.toFixed(2)}, ${y.toFixed(2)}) escala: ${scale.toFixed(3)}`);
            }
        }
        catch (error) {
            console.error("Erro ao processar atualização de posição:", error);
        }
    }
    processLeaderboard(dataView, viewId) {
        try {
            const world = World.getInstance();
            const leaderboard = [];
            let offset = 1; // Pula o opcode
            // Lê número de entradas no leaderboard
            const count = dataView.getUint32(offset, true);
            offset += 4;
            for (let i = 0; i < count && i < 10; i++) {
                // Lê ID da célula (se aplicável)
                const cellId = dataView.getUint32(offset, true);
                offset += 4;
                // Lê nome do jogador
                const nameLength = dataView.getUint16(offset, true);
                offset += 2;
                let name = "";
                if (nameLength > 0) {
                    const nameBytes = new Uint16Array(dataView.buffer, offset, nameLength / 2);
                    name = String.fromCharCode(...nameBytes);
                    offset += nameLength;
                }
                leaderboard.push({ position: i + 1, cellId, name });
            }
            // Atualiza leaderboard no mundo
            world.updateLeaderboard(viewId, leaderboard, performance.now());
            if (CONFIG.DEFAULT_SETTINGS.enableDebugMode) {
                console.log("🏆 Leaderboard atualizado:", leaderboard);
            }
        }
        catch (error) {
            console.error("Erro ao processar leaderboard:", error);
        }
    }
    processClearCells(dataView, viewId) {
        try {
            const world = World.getInstance();
            const vision = world.createVision(viewId);
            // Remove todas as células próprias
            const ownedCells = Array.from(vision.owned);
            for (const cellId of ownedCells) {
                world.removeOwnCell(cellId, viewId);
            }
            if (CONFIG.DEFAULT_SETTINGS.enableDebugMode) {
                console.log(`🧹 ${ownedCells.length} células próprias removidas`);
            }
        }
        catch (error) {
            console.error("Erro ao processar limpeza de células:", error);
        }
    }
    processRemoveCells(dataView, viewId) {
        try {
            const world = World.getInstance();
            const timestamp = performance.now();
            let offset = 1; // Pula o opcode
            // Lê número de células a remover
            const count = dataView.getUint16(offset, true);
            offset += 2;
            for (let i = 0; i < count; i++) {
                const cellId = dataView.getUint32(offset, true);
                offset += 4;
                world.removeCell(cellId, viewId, timestamp);
            }
            if (CONFIG.DEFAULT_SETTINGS.enableDebugMode) {
                console.log(`❌ ${count} células removidas`);
            }
        }
        catch (error) {
            console.error("Erro ao processar remoção de células:", error);
        }
    }
    processMapBorders(dataView, viewId) {
        try {
            const left = dataView.getFloat64(1, true);
            const top = dataView.getFloat64(9, true);
            const right = dataView.getFloat64(17, true);
            const bottom = dataView.getFloat64(25, true);
            // Atualiza limites do mapa no mundo
            const world = World.getInstance();
            world.updateMapBorders(viewId, left, top, right, bottom);
            if (CONFIG.DEFAULT_SETTINGS.enableDebugMode) {
                console.log(`🗺️ Limites do mapa - L:${left} T:${top} R:${right} B:${bottom}`);
            }
        }
        catch (error) {
            console.error("Erro ao processar limites do mapa:", error);
        }
    }
    processOwnCells(dataView, viewId, timestamp) {
        try {
            const world = World.getInstance();
            let offset = 1; // Pula o opcode
            // Lê número de células próprias
            const count = dataView.getUint32(offset, true);
            offset += 4;
            for (let i = 0; i < count; i++) {
                const cellId = dataView.getUint32(offset, true);
                offset += 4;
                // Adiciona célula própria ao mundo
                world.addOwnCell(cellId, viewId, timestamp);
                this.emit({
                    type: EventType.CELL_SPAWN,
                    timestamp,
                    viewId,
                    cellId,
                    position: { x: 0, y: 0 }, // Será atualizado na próxima atualização do mundo
                    radius: 0, // Será atualizado na próxima atualização do mundo
                });
            }
            if (CONFIG.DEFAULT_SETTINGS.enableDebugMode) {
                console.log(`👤 ${count} células próprias adicionadas`);
            }
        }
        catch (error) {
            console.error("Erro ao processar células próprias:", error);
        }
    }
    processChatMessage(dataView, viewId) {
        try {
            let offset = 1; // Pula o opcode
            // Lê flags da mensagem
            const flags = dataView.getUint8(offset++);
            const isServerMessage = (flags & 0x80) !== 0;
            const isAdminMessage = (flags & 0x40) !== 0;
            // Lê cor da mensagem (se aplicável)
            let color = "#FFFFFF";
            if (!isServerMessage) {
                const r = dataView.getUint8(offset++);
                const g = dataView.getUint8(offset++);
                const b = dataView.getUint8(offset++);
                color = `rgb(${r},${g},${b})`;
            }
            // Lê nome do remetente
            const nameLength = dataView.getUint16(offset, true);
            offset += 2;
            let senderName = "";
            if (nameLength > 0) {
                const nameBytes = new Uint16Array(dataView.buffer, offset, nameLength / 2);
                senderName = String.fromCharCode(...nameBytes);
                offset += nameLength;
            }
            // Lê mensagem
            const messageLength = dataView.getUint16(offset, true);
            offset += 2;
            let message = "";
            if (messageLength > 0) {
                const messageBytes = new Uint16Array(dataView.buffer, offset, messageLength / 2);
                message = String.fromCharCode(...messageBytes);
            }
            if (CONFIG.DEFAULT_SETTINGS.enableDebugMode) {
                console.log(`💬 Chat [${senderName}]: ${message}`, {
                    isServerMessage,
                    isAdminMessage,
                    color,
                });
            }
        }
        catch (error) {
            console.error("Erro ao processar mensagem de chat:", error);
        }
    }
    processServerStats(dataView, viewId, connection, timestamp) {
        try {
            let offset = 1; // Pula o opcode
            // Lê estatísticas do servidor
            const playersOnline = dataView.getUint16(offset, true);
            offset += 2;
            const playersLimit = dataView.getUint16(offset, true);
            offset += 2;
            // Calcula latência se o ping foi enviado
            if (connection.lastPingTime !== undefined) {
                connection.latency = timestamp - connection.lastPingTime;
                if (CONFIG.DEFAULT_SETTINGS.enableDebugMode) {
                    console.log(`🏓 Latência: ${connection.latency.toFixed(1)}ms | Jogadores: ${playersOnline}/${playersLimit}`);
                }
            }
            connection.lastPingTime = undefined;
            // Atualiza estatísticas no mundo
            const world = World.getInstance();
            const vision = world.createVision(viewId);
            vision.stats = {
                playersOnline,
                playersLimit,
                latency: connection.latency || 0,
                timestamp,
            };
        }
        catch (error) {
            console.error("Erro ao processar estatísticas do servidor:", error);
        }
    }
    processPasswordError() {
        console.error("🔒 Falha na autenticação da senha!");
        // Implementar lógica de nova tentativa de senha se necessário
    }
    // ===== GERENCIAMENTO DO ESTADO DA CONEXÃO =====
    /** Reseta o estado da conexão após desconexão */
    resetConnectionState(connection) {
        connection.handshake = undefined;
        connection.latency = undefined;
        connection.lastPingTime = undefined;
        connection.playBlock = undefined;
        connection.rejectionCount++;
        connection.websocket = undefined;
        if (connection.retryCount > 0) {
            connection.retryCount--;
        }
    }
    /** Apenas registra desconexão - não tenta reconectar */
    attemptReconnection(viewId, connection) {
        // Apenas interceptamos - não tentamos reconectar
        console.log("🔍 Conexão perdida para viewId:", viewId.toString(), "- aguardando nova conexão do jogo");
        this.updateConnectionState(viewId, ConnectionState.DISCONNECTED, "Aguardando reconexão do jogo");
    }
    // ===== TAREFAS PERIÓDICAS =====
    /** Inicia tarefas periódicas para monitoramento e atualizações */
    startPeriodicTasks() {
        this.startPingMonitoring();
        this.startConnectionMonitoring();
        this.startQuestTimers();
    }
    /** Envia mensagens periodicas de ping para medir latência */
    startPingMonitoring() {
        setInterval(() => {
            for (const connection of this.connections.values()) {
                if (!this.isConnectionReady(connection))
                    continue;
                // marca a conexão como não responsiva se o ping anterior não foi respondido
                if (connection.lastPingTime !== undefined)
                    connection.latency = -1;
                connection.lastPingTime = performance.now();
                this.sendPing(connection);
            }
        }, WebSocketInterceptor.PING_INTERVAL);
    }
    /** Monitora as conexões WebSocket apenas para logging - não interfere */
    startConnectionMonitoring() {
        setInterval(() => {
            // Apenas monitora para logging - não interfere nas conexões
            const activeConnections = Array.from(this.connections.values()).filter(conn => conn.websocket && this.isConnectionActive(conn.websocket));
            if (CONFIG.DEFAULT_SETTINGS.enableDebugMode && activeConnections.length > 0) {
                console.log(`🔍 Monitorando ${activeConnections.length} conexões ativas`);
            }
        }, WebSocketInterceptor.GAMEMODE_CHECK_INTERVAL);
    }
    /** Envia periodicamente atualizações de tempo da missão */
    startQuestTimers() {
        setInterval(() => {
            for (const viewId of this.connections.keys()) {
                this.sendQuestTime(viewId);
            }
        }, WebSocketInterceptor.QUEST_TIMER_INTERVAL);
    }
    // ===== UTILITY METHODS =====
    /** Verifica se a conexão está pronta para comunicação */
    isConnectionReady(connection) {
        var _a;
        return !!(connection.handshake && ((_a = connection.websocket) === null || _a === void 0 ? void 0 : _a.readyState) === WebSocket.OPEN);
    }
    /** Verifica se a conexão WebSocket está ativa */
    isConnectionActive(websocket) {
        return websocket.readyState === WebSocket.CONNECTING || websocket.readyState === WebSocket.OPEN;
    }
    /** Envia mensagem ping para a conexão */
    sendPing(connection) {
        if (!connection.handshake || !connection.websocket)
            return;
        const pingOpcode = 0xfe;
        const pingData = connection.handshake.encryptTable.slice(pingOpcode, pingOpcode + 1);
        connection.websocket.send(pingData);
    }
    /** Envia mensagem opcode simples */
    sendOpcode(viewId, opcode) {
        const connection = this.connections.get(viewId);
        if (!this.isConnectionReady(connection))
            return;
        const opcodeData = connection.handshake.encryptTable.slice(opcode, opcode + 1);
        connection.websocket.send(opcodeData);
    }
    /** Logs message for debugging and analysis */
    logMessage(viewId, data, direction) {
        this.messageLog.push({
            timestamp: performance.now(),
            viewId,
            data,
            direction,
        });
        // Limite de tamanho do buffer de mensagens
        if (this.messageLog.length > WebSocketInterceptor.MESSAGE_BUFFER_LIMIT) {
            this.messageLog.splice(0, this.messageLog.length - WebSocketInterceptor.MESSAGE_BUFFER_TRIM_SIZE);
        }
    }
    /** Busca ou cria um identificador de visualização */
    findOrCreateViewId() {
        // TODO: Implementar gerenciamento de visualizações baseado em abas do jogo
        return Symbol("primary-view");
    }
    /** Retorna a URL do servidor de jogo atual */
    getCurrentGameUrl() {
        return `ws://${CONFIG.NETWORK.WEBSOCKET_URL}`;
    }
    /** Exibe mensagem de erro de conexão */
    showConnectionError() {
        console.error("❌ Connection failed: Check server address or security settings");
    }
    // ===== PUBLIC API =====
    /** Envia atualização de tempo da missão para a visualização especificada */
    sendQuestTime(viewId) {
        const questTimeOpcode = 0xbf;
        this.sendOpcode(viewId, questTimeOpcode);
    }
    /** Obtém informações sobre todas as conexões ativas */
    getAllConnectionsInfo() {
        return Array.from(this.connections.entries()).map(([viewId, connection]) => {
            var _a, _b;
            return ({
                viewId,
                url: (_a = connection.websocket) === null || _a === void 0 ? void 0 : _a.url,
                readyState: (_b = connection.websocket) === null || _b === void 0 ? void 0 : _b.readyState,
                latency: connection.latency,
                rejectionCount: connection.rejectionCount,
                retryCount: connection.retryCount,
                state: this.getConnectionState(viewId),
            });
        });
    }
    /** Obtém as mensagens recentes do log */
    getRecentMessages(maxAgeSeconds = 10) {
        const cutoffTime = performance.now() - maxAgeSeconds * 1000;
        return this.messageLog.filter(entry => entry.timestamp > cutoffTime);
    }
    /** Obtém informações de estatísticas de rede */
    getNetworkStats() {
        const connections = Array.from(this.connections.values());
        const activeConnections = connections.filter(c => { var _a; return ((_a = c.websocket) === null || _a === void 0 ? void 0 : _a.readyState) === WebSocket.OPEN; });
        const validLatencies = connections.map(c => c.latency).filter((lat) => lat !== undefined && lat > 0);
        return {
            totalConnections: this.connections.size,
            activeConnections: activeConnections.length,
            totalMessages: this.messageLog.length,
            averageLatency: validLatencies.length > 0 ? validLatencies.reduce((sum, lat) => sum + lat, 0) / validLatencies.length : 0,
            uptime: performance.now() - this.startTime,
        };
    }
    /** Obtém o estado atual da conexão */
    getConnectionState(viewId) {
        const connection = this.connections.get(viewId);
        if (!(connection === null || connection === void 0 ? void 0 : connection.websocket))
            return ConnectionState.DISCONNECTED;
        switch (connection.websocket.readyState) {
            case WebSocket.CONNECTING:
                return ConnectionState.CONNECTING;
            case WebSocket.OPEN:
                return ConnectionState.CONNECTED;
            case WebSocket.CLOSING:
            case WebSocket.CLOSED:
                return connection.retryCount > 0 ? ConnectionState.RECONNECTING : ConnectionState.DISCONNECTED;
            default:
                return ConnectionState.ERROR;
        }
    }
    /** Atualiza e emite mudança de estado da conexão */
    updateConnectionState(viewId, newState, reason) {
        const oldState = this.connectionStates.get(viewId) || ConnectionState.DISCONNECTED;
        if (oldState !== newState) {
            this.connectionStates.set(viewId, newState);
            this.emit({
                type: EventType.CONNECTION_STATE_CHANGE,
                timestamp: performance.now(),
                viewId,
                oldState,
                newState,
                reason,
            });
        }
    }
}
// Constantes de configuração
WebSocketInterceptor.PING_INTERVAL = CONFIG.NETWORK.PING_INTERVAL;
WebSocketInterceptor.GAMEMODE_CHECK_INTERVAL = 200;
WebSocketInterceptor.QUEST_TIMER_INTERVAL = 1000;
WebSocketInterceptor.MESSAGE_BUFFER_LIMIT = 1000;
WebSocketInterceptor.MESSAGE_BUFFER_TRIM_SIZE = 500;
class World {
    constructor() {
        // Armazenamento principal de entidades do jogo
        this.cells = new Map();
        this.pellets = new Map();
        // Símbolos únicos para identificar diferentes abas/visualizações
        this.multis = Array.from({ length: 8 }, () => Symbol());
        // IDs das visualizações principais
        this.viewId = {
            primary: this.multis[0],
            secondary: this.multis[1],
            spectate: Symbol(),
        };
        // Visualização atualmente selecionada
        this.selected = this.viewId.primary;
        // Mapa de todas as visualizações ativas
        this.views = new Map();
        // Estado de sincronização entre abas
        this.synchronized = false;
        this.wasFlawlessSynchronized = false;
        this.lastClean = performance.now();
        // Estatísticas do jogador
        this.stats = {
            foodEaten: 0,
            highestPosition: 200,
            highestScore: 0,
            spawnedAt: undefined,
        };
        this.settings = CONFIG.DEFAULT_SETTINGS;
    }
    // Padrão Singleton para garantir uma única instância
    static getInstance() {
        if (!World.instance) {
            World.instance = new World();
        }
        return World.instance;
    }
    // ===== MÉTODOS PÚBLICOS =====
    // Verifica se há células vivas em qualquer visualização
    isAlive() {
        var _a;
        for (const [view, vision] of this.views) {
            for (const id of vision.owned) {
                const cell = this.cells.get(id);
                // Se a célula não existe ainda, consideramos como viva
                if (!cell)
                    return true;
                const frame = (_a = cell.views.get(view)) === null || _a === void 0 ? void 0 : _a.frames[0];
                if ((frame === null || frame === void 0 ? void 0 : frame.deadAt) === undefined)
                    return true;
            }
        }
        return false;
    }
    // Atualiza uma célula no mundo
    updateCell(cellId, cellData, viewId, timestamp) {
        let cell = this.cells.get(cellId);
        if (!cell) {
            cell = { views: new Map() };
            this.cells.set(cellId, cell);
        }
        let record = cell.views.get(viewId);
        if (!record) {
            record = { frames: [] };
            cell.views.set(viewId, record);
        }
        const frame = {
            nx: cellData.position.x,
            ny: cellData.position.y,
            nr: cellData.radius,
            born: timestamp,
            deadAt: cellData.isAlive ? undefined : timestamp,
            deadTo: 0,
            ox: cellData.position.x,
            oy: cellData.position.y,
            or: cellData.radius,
            jr: cellData.radius,
            a: cellData.isAlive ? 1 : 0,
            updated: timestamp,
        };
        // Adiciona o frame no início do array
        record.frames.unshift(frame);
        // Mantém apenas os frames mais recentes
        if (record.frames.length > 10) {
            record.frames = record.frames.slice(0, 10);
        }
    }
    // Atualiza um pellet no mundo
    updatePellet(pelletId, pelletData, viewId, timestamp) {
        let pellet = this.pellets.get(pelletId);
        if (!pellet) {
            pellet = { views: new Map() };
            this.pellets.set(pelletId, pellet);
        }
        let record = pellet.views.get(viewId);
        if (!record) {
            record = { frames: [] };
            pellet.views.set(viewId, record);
        }
        const frame = {
            nx: pelletData.position.x,
            ny: pelletData.position.y,
            nr: pelletData.radius,
            born: timestamp,
            deadAt: pelletData.isAlive ? undefined : timestamp,
            deadTo: 0,
            ox: pelletData.position.x,
            oy: pelletData.position.y,
            or: pelletData.radius,
            jr: pelletData.radius,
            a: pelletData.isAlive ? 1 : 0,
            updated: timestamp,
        };
        record.frames.unshift(frame);
        // Mantém apenas o frame mais recente para pellets
        if (record.frames.length > 1) {
            record.frames = record.frames.slice(0, 1);
        }
    }
    // Remove uma célula do mundo
    removeCell(cellId, viewId, timestamp) {
        const cell = this.cells.get(cellId);
        if (cell) {
            const record = cell.views.get(viewId);
            if (record && record.frames.length > 0) {
                const lastFrame = record.frames[0];
                if (lastFrame.deadAt === undefined) {
                    lastFrame.deadAt = timestamp;
                    lastFrame.a = 0;
                }
            }
        }
        // Remove também dos pellets se existir
        const pellet = this.pellets.get(cellId);
        if (pellet) {
            const record = pellet.views.get(viewId);
            if (record && record.frames.length > 0) {
                const lastFrame = record.frames[0];
                if (lastFrame.deadAt === undefined) {
                    lastFrame.deadAt = timestamp;
                    lastFrame.a = 0;
                }
            }
        }
    }
    // Adiciona uma célula própria
    addOwnCell(cellId, viewId, timestamp) {
        const vision = this.createVision(viewId);
        vision.owned.add(cellId);
        vision.spawned = timestamp;
        // Atualiza estatísticas
        if (this.stats.spawnedAt === undefined) {
            this.stats.spawnedAt = timestamp;
        }
    }
    // Remove uma célula própria
    removeOwnCell(cellId, viewId) {
        const vision = this.views.get(viewId);
        if (vision) {
            vision.owned.delete(cellId);
        }
    }
    // Atualiza posição da câmera
    updateCameraPosition(viewId, x, y, scale, timestamp) {
        const vision = this.createVision(viewId);
        vision.camera.tx = x;
        vision.camera.ty = y;
        vision.camera.tscale = scale;
        vision.camera.updated = timestamp;
        vision.used = timestamp;
    }
    // Atualiza leaderboard
    updateLeaderboard(viewId, leaderboard, timestamp) {
        const vision = this.createVision(viewId);
        vision.leaderboard = leaderboard;
        vision.used = timestamp;
    }
    // Atualiza limites do mapa
    updateMapBorders(viewId, left, top, right, bottom) {
        const vision = this.createVision(viewId);
        vision.border = { left, top, right, bottom };
    }
    // Calcula a câmera para uma visualização específica
    calculateSingleCamera(view, vision, weightExponent = 0, now = performance.now()) {
        var _a, _b;
        vision = vision !== null && vision !== void 0 ? vision : this.views.get(view);
        if (!vision) {
            return { mass: 0, scale: 1, sumX: 0, sumY: 0, weight: 0 };
        }
        let mass = 0;
        let r = 0;
        let sumX = 0;
        let sumY = 0;
        let weight = 0;
        // Processa todas as células possuídas por esta visualização
        for (const id of vision.owned) {
            const cell = this.cells.get(id);
            const frame = this.synchronized ? cell === null || cell === void 0 ? void 0 : cell.merged : (_a = cell === null || cell === void 0 ? void 0 : cell.views.get(view)) === null || _a === void 0 ? void 0 : _a.frames[0];
            const interp = this.synchronized ? cell === null || cell === void 0 ? void 0 : cell.merged : (_b = cell === null || cell === void 0 ? void 0 : cell.views.get(view)) === null || _b === void 0 ? void 0 : _b.frames[0];
            if (!frame || !interp)
                continue;
            // Não incluir células possuídas antes do respawn
            if (frame.born < vision.spawned)
                continue;
            if (this.settings.cameraMovement === "instant") {
                const xyr = this.calculatePosition(frame, interp, undefined, undefined, false, now);
                r += xyr.r * xyr.a;
                mass += ((xyr.r * xyr.r) / 100) * xyr.a;
                const cellWeight = xyr.a * Math.pow(xyr.r, weightExponent);
                sumX += xyr.x * cellWeight;
                sumY += xyr.y * cellWeight;
                weight += cellWeight;
            }
            else {
                // Movimento de câmera padrão
                if (frame.deadAt !== undefined)
                    continue;
                const xyr = this.calculatePosition(frame, interp, undefined, undefined, false, now);
                r += frame.nr;
                mass += (frame.nr * frame.nr) / 100;
                const cellWeight = Math.pow(frame.nr, weightExponent);
                sumX += xyr.x * cellWeight;
                sumY += xyr.y * cellWeight;
                weight += cellWeight;
            }
        }
        const scale = Math.pow(Math.min(64 / r, 1), 0.4);
        return { mass, scale, sumX, sumY, weight };
    }
    // Calcula e atualiza as posições de câmera para todas as visualizações
    updateCameras(now = performance.now()) {
        const weightExponent = this.settings.camera !== "default" ? 2 : 0;
        // Cria conjuntos disjuntos de todas as câmeras próximas
        const cameras = new Map();
        const sets = new Map();
        for (const [view, vision] of this.views) {
            cameras.set(view, this.calculateSingleCamera(view, vision, weightExponent, now));
            sets.set(view, new Set([view]));
        }
        // Calcula mesmo se as abas não forem realmente mescladas (para contornos multi)
        if (this.settings.multibox || this.settings.nbox) {
            this.mergeCameraSets(cameras, sets, now);
        }
        // Calcula e atualiza posições de câmera mescladas
        this.updateMergedCameras(cameras, sets, now);
    }
    // Cria ou retorna uma visualização existente
    createVision(view) {
        const existing = this.views.get(view);
        if (existing)
            return existing;
        const vision = {
            border: undefined,
            camera: {
                x: 0,
                tx: 0,
                y: 0,
                ty: 0,
                scale: 0,
                tscale: 0,
                merged: false,
                updated: performance.now() - 1,
            },
            leaderboard: [],
            owned: new Set(),
            spawned: -Infinity,
            stats: undefined,
            used: -Infinity,
        };
        this.views.set(view, vision);
        return vision;
    }
    // Sincroniza frames entre diferentes visualizações
    synchronizeViews() {
        if (this.wasFlawlessSynchronized && this.settings.synchronization !== "flawless")
            this.cleanupFrameHistory();
        if (!this.settings.synchronization || this.views.size <= 1) {
            this.resetSynchronization();
            return;
        }
        const now = performance.now();
        const indices = {};
        if (this.settings.synchronization === "flawless") {
            this.performFlawlessSynchronization(indices, now);
        }
        else {
            this.performLatestSynchronization(indices);
        }
        this.mergeFrames(indices, now);
        this.cleanupFrameHistory();
        this.updateSynchronizationState(now);
    }
    calculateScore(view) {
        var _a;
        let score = 0;
        const vision = this.views.get(view);
        if (!vision)
            return 0;
        for (const id of vision.owned) {
            const cell = this.cells.get(id);
            if (!cell)
                continue;
            const frame = this.synchronized ? cell.merged : (_a = cell.views.get(view)) === null || _a === void 0 ? void 0 : _a.frames[0];
            if (!frame || frame.deadAt !== undefined)
                continue;
            // Usa pontuação exata do servidor, sem interpolação
            score += (frame.nr * frame.nr) / 100;
        }
        return score;
    }
    // Calcula posição interpolada de uma célula
    calculatePosition(frame, interp, killerFrame, killerInterp, isPellet = false, now = performance.now()) {
        let nx = frame.nx;
        let ny = frame.ny;
        // Anima em direção à posição interpolada do assassino para suavidade extra
        if (killerFrame && killerInterp) {
            const killerXyr = this.calculatePosition(killerFrame, killerInterp, undefined, undefined, false, now);
            nx = killerXyr.x;
            ny = killerXyr.y;
        }
        let x, y, r, a;
        if (isPellet && frame.deadAt === undefined) {
            // Pellets não se movem suavemente
            x = nx;
            y = ny;
            r = frame.nr;
            a = 1;
        }
        else {
            // Interpolação suave para células
            let alpha = (now - interp.updated) / this.settings.drawDelay;
            alpha = Math.max(0, Math.min(1, alpha));
            x = interp.ox + (nx - interp.ox) * alpha;
            y = interp.oy + (ny - interp.oy) * alpha;
            r = interp.or + (frame.nr - interp.or) * alpha;
            const targetA = frame.deadAt !== undefined ? 0 : 1;
            a = interp.a + (targetA - interp.a) * alpha;
        }
        const dt = (now - interp.updated) / 1000;
        const jellyPhysicsSpeed = this.settings.slowerJellyPhysics ? 10 : 5;
        return {
            x,
            y,
            r,
            jr: this.exponentialEase(interp.jr, r, jellyPhysicsSpeed, dt),
            a,
        };
    }
    // Remove células mortas e invisíveis
    cleanupDeadCells() {
        const now = performance.now();
        if (now - this.lastClean < 200)
            return;
        this.lastClean = now;
        for (const collection of [this.cells, this.pellets]) {
            for (const [id, cell] of collection) {
                for (const [view, record] of cell.views) {
                    const firstFrame = record.frames[0];
                    const lastFrame = record.frames[record.frames.length - 1];
                    if (firstFrame.deadAt !== lastFrame.deadAt)
                        continue;
                    if (lastFrame.deadAt !== undefined && now - lastFrame.deadAt >= this.settings.drawDelay + 200) {
                        cell.views.delete(view);
                    }
                }
                if (cell.views.size === 0) {
                    collection.delete(id);
                }
            }
        }
    }
    // ===== MÉTODOS PRIVADOS =====
    // Mescla conjuntos de câmeras próximas
    mergeCameraSets(cameras, sets, now) {
        for (const [view, vision] of this.views) {
            const set = sets.get(view);
            const camera = cameras.get(view);
            if (camera.weight <= 0 || now - vision.used > 20000)
                continue;
            const x = camera.sumX / camera.weight;
            const y = camera.sumY / camera.weight;
            const width = 1920 / 2 / camera.scale;
            const height = 1080 / 2 / camera.scale;
            for (const [otherView, otherVision] of this.views) {
                const otherSet = sets.get(otherView);
                if (set === otherSet || now - otherVision.used > 20000)
                    continue;
                const otherCamera = cameras.get(otherView);
                if (otherCamera.weight <= 0)
                    continue;
                const otherX = otherCamera.sumX / otherCamera.weight;
                const otherY = otherCamera.sumY / otherCamera.weight;
                const otherWidth = 1920 / 2 / otherCamera.scale;
                const otherHeight = 1080 / 2 / otherCamera.scale;
                // Limite de proximidade baseado na massa de ambas as abas
                const threshold = 1000 + Math.min(camera.weight / 100 / 25, otherCamera.weight / 100 / 25);
                if (Math.abs(x - otherX) <= width + otherWidth + threshold && Math.abs(y - otherY) <= height + otherHeight + threshold) {
                    // Mesclar conjuntos disjuntos
                    for (const connectedView of otherSet) {
                        set.add(connectedView);
                        sets.set(connectedView, set);
                    }
                }
            }
        }
    }
    // Atualiza câmeras mescladas
    updateMergedCameras(cameras, sets, now) {
        const computed = new Set();
        for (const set of sets.values()) {
            if (computed.has(set))
                continue;
            let mass = 0;
            let sumX = 0;
            let sumY = 0;
            let weight = 0;
            if (this.settings.mergeCamera) {
                for (const view of set) {
                    const camera = cameras.get(view);
                    mass += camera.mass;
                    sumX += camera.sumX;
                    sumY += camera.sumY;
                    weight += camera.weight;
                }
            }
            for (const view of set) {
                const vision = this.views.get(view);
                if (!this.settings.mergeCamera) {
                    const camera = cameras.get(view);
                    ({ mass, sumX, sumY, weight } = camera);
                }
                this.updateSingleCameraPosition(vision, mass, sumX, sumY, weight, set.size > 1, now);
            }
            computed.add(set);
        }
    }
    // Atualiza posição de uma única câmera
    updateSingleCameraPosition(vision, mass, sumX, sumY, weight, isMerged, now) {
        let xyFactor;
        if (weight <= 0) {
            xyFactor = 20;
        }
        else if (this.settings.cameraMovement === "instant") {
            xyFactor = 1;
        }
        else {
            // Movimento de câmera suave após spawnar
            const aliveFor = (performance.now() - vision.spawned) / 1000;
            const a = Math.max(0, Math.min(1, (aliveFor - 0.3) / 0.3));
            const base = this.settings.cameraSpawnAnimation ? 2 : 1;
            xyFactor = Math.min(this.settings.cameraSmoothness, base * (1 - a) + this.settings.cameraSmoothness * a);
        }
        if (weight > 0) {
            vision.camera.tx = sumX / weight;
            vision.camera.ty = sumY / weight;
            let scale;
            if (this.settings.camera === "default") {
                scale = Math.pow(Math.min(64 / Math.sqrt(mass), 1), 0.4);
            }
            else {
                scale = Math.pow(Math.min(64 / Math.sqrt(100 * mass), 1), 0.4);
            }
            vision.camera.tscale = this.settings.autoZoom ? scale : 0.25;
        }
        const dt = (now - vision.camera.updated) / 1000;
        vision.camera.x = this.exponentialEase(vision.camera.x, vision.camera.tx, xyFactor, dt);
        vision.camera.y = this.exponentialEase(vision.camera.y, vision.camera.ty, xyFactor, dt);
        vision.camera.scale = this.exponentialEase(vision.camera.scale, vision.camera.tscale, 9, dt);
        vision.camera.merged = isMerged;
        vision.camera.updated = now;
    }
    // Implementa sincronização perfeita entre visualizações (versão simplificada)
    performFlawlessSynchronization(indices, now) {
        // Versão simplificada da sincronização perfeita
        // Em uma implementação real, isso envolveria algoritmos complexos de grafos bipartidos
        let i = 0;
        for (const view of this.views.keys()) {
            indices[i++] = indices[view] = 0; // Por simplicidade, usar sempre índice 0
        }
        this.wasFlawlessSynchronized = true;
    }
    // Implementa sincronização usando frames mais recentes
    performLatestSynchronization(indices) {
        let i = 0;
        for (const view of this.views.keys()) {
            indices[i++] = indices[view] = 0;
        }
        this.wasFlawlessSynchronized = false;
    }
    // Mescla frames baseado nos índices encontrados
    mergeFrames(indices, now) {
        for (const collection of [this.cells, this.pellets]) {
            for (const cell of collection.values()) {
                // Encontra frame modelo (versão simplificada)
                let modelFrame;
                for (const [view, record] of cell.views) {
                    const frame = record.frames[indices[view] || 0];
                    if (frame && !modelFrame) {
                        modelFrame = frame;
                    }
                }
                if (modelFrame) {
                    cell.model = modelFrame;
                }
            }
        }
        // Atualiza frames mesclados
        this.updateMergedFrames(now);
    }
    // Atualiza frames mesclados para todas as células
    updateMergedFrames(now) {
        var _a;
        for (const collection of [this.cells, this.pellets]) {
            for (const cell of collection.values()) {
                const { model, merged } = cell;
                if (!model) {
                    cell.merged = undefined;
                    continue;
                }
                if (!merged || (merged.deadAt !== undefined && model.deadAt === undefined)) {
                    // Cria novo frame mesclado
                    cell.merged = {
                        nx: model.nx,
                        ny: model.ny,
                        nr: model.nr,
                        born: now,
                        deadAt: model.deadAt !== undefined ? now : undefined,
                        deadTo: model.deadTo,
                        ox: model.nx,
                        oy: model.ny,
                        or: model.nr,
                        jr: model.nr,
                        a: 0,
                        updated: now,
                    };
                }
                else {
                    // Atualiza frame mesclado existente
                    if (merged.deadAt === undefined &&
                        (model.deadAt !== undefined || model.nx !== merged.nx || model.ny !== merged.ny || model.nr !== merged.nr)) {
                        const isPellet = collection === this.pellets;
                        const xyr = this.calculatePosition(merged, merged, undefined, undefined, isPellet, now);
                        merged.ox = xyr.x;
                        merged.oy = xyr.y;
                        merged.or = xyr.r;
                        merged.jr = xyr.jr;
                        merged.a = xyr.a;
                        merged.updated = now;
                    }
                    merged.nx = model.nx;
                    merged.ny = model.ny;
                    merged.nr = model.nr;
                    merged.deadAt = model.deadAt !== undefined ? (_a = merged.deadAt) !== null && _a !== void 0 ? _a : now : undefined;
                    merged.deadTo = model.deadTo;
                }
            }
        }
    }
    // Remove histórico de frames
    cleanupFrameHistory() {
        for (const collection of [this.cells, this.pellets]) {
            for (const cell of collection.values()) {
                for (const record of cell.views.values()) {
                    // Remove frames antigos, mantendo apenas o atual
                    while (record.frames.length > 1) {
                        record.frames.pop();
                    }
                }
            }
        }
    }
    // Reset do estado de sincronização
    resetSynchronization() {
        this.disagreementStart = undefined;
        this.disagreementAt = undefined;
        this.synchronized = false;
        this.wasFlawlessSynchronized = false;
    }
    // Atualiza estado de sincronização
    updateSynchronizationState(now) {
        this.disagreementStart = undefined;
        // Se houve desacordo, espera um tempo antes de reativar sincronização
        if (this.disagreementAt === undefined || now - this.disagreementAt > 1000) {
            this.synchronized = true;
        }
    }
    // Utilitário para easing exponencial
    exponentialEase(current, target, factor, deltaTime) {
        return current + (target - current) * (1 - Math.exp(-factor * deltaTime));
    }
    // ===== GETTERS PÚBLICOS =====
    get gameStats() {
        return Object.assign({}, this.stats);
    }
    get currentView() {
        return this.selected;
    }
    get allViews() {
        return this.views;
    }
    get isSynchronized() {
        return this.synchronized;
    }
    get cellCount() {
        return this.cells.size;
    }
    get pelletCount() {
        return this.pellets.size;
    }
    // Obtém estatísticas detalhadas do mundo
    getDetailedStats() {
        var _a;
        let totalOwnedCells = 0;
        let totalScore = 0;
        let totalLatency = 0;
        let latencyCount = 0;
        for (const [viewId, vision] of this.views) {
            totalOwnedCells += vision.owned.size;
            totalScore += this.calculateScore(viewId);
            if ((_a = vision.stats) === null || _a === void 0 ? void 0 : _a.latency) {
                totalLatency += vision.stats.latency;
                latencyCount++;
            }
        }
        return {
            cells: this.cellCount,
            pellets: this.pelletCount,
            views: this.views.size,
            ownedCells: totalOwnedCells,
            totalScore,
            averageLatency: latencyCount > 0 ? totalLatency / latencyCount : 0,
            isAlive: this.isAlive(),
            isSynchronized: this.isSynchronized,
            uptime: performance.now() - (this.stats.spawnedAt || performance.now()),
        };
    }
    // Obtém informações de uma visualização específica
    getViewInfo(viewId) {
        const vision = this.views.get(viewId);
        if (!vision)
            return null;
        return {
            ownedCells: Array.from(vision.owned),
            score: this.calculateScore(viewId),
            camera: Object.assign({}, vision.camera),
            leaderboard: [...vision.leaderboard],
            stats: vision.stats ? Object.assign({}, vision.stats) : undefined,
            isActive: performance.now() - vision.used < 5000, // Ativa se usada nos últimos 5 segundos
        };
    }
}
class UIManager {
    constructor() {
        this.components = new Map();
        this.isInitialized = false;
        this.debugPanelVisible = false;
        this.keyBindManager = KeyBindManager.getInstance();
    }
    static getInstance() {
        if (!UIManager.instance) {
            UIManager.instance = new UIManager();
        }
        return UIManager.instance;
    }
    initialize() {
        if (this.isInitialized)
            return true;
        this.setupKeyBindings();
        this.createDebugPanel();
        this.isInitialized = true;
        return true;
    }
    setupKeyBindings() {
        this.keyBindManager.register({
            key: "F1",
            handler: () => this.showHelpDialog(),
            description: "Mostra a ajuda do script",
        });
        this.keyBindManager.register({
            key: "F2",
            handler: () => this.showDebugPanel(),
            description: "Mostra/oculta painel de debug",
        });
        this.keyBindManager.register({
            key: "F3",
            handler: () => {
                const app = ScriptApplication.getInstance();
                const status = app.getStatus();
                console.log("📊 Status da aplicação:", status);
            },
            description: "Mostra status da aplicação",
        });
        this.keyBindManager.register({
            key: "F4",
            handler: () => {
                const world = World.getInstance();
                const detailedStats = world.getDetailedStats();
                console.group("🌍 Estado Detalhado do Mundo");
                console.log("📊 Estatísticas Gerais:", detailedStats);
                // Mostra informações de cada visualização
                for (const [viewId, _] of world.allViews) {
                    const viewInfo = world.getViewInfo(viewId);
                    if (viewInfo) {
                        console.log(`👁️ Visualização ${viewId.toString()}:`, viewInfo);
                    }
                }
                console.groupEnd();
            },
            description: "Mostra estado detalhado do mundo",
        });
    }
    createDebugPanel() {
        // Implementação básica do painel de debug
        // Em uma implementação completa, isso seria um componente mais elaborado
    }
    showDebugPanel() {
        if (this.debugPanelVisible) {
            this.hideDebugPanel();
            return;
        }
        const interceptor = WebSocketInterceptor.getInstance();
        const info = interceptor.getAllConnectionsInfo();
        const stats = interceptor.getNetworkStats();
        const recent = interceptor.getRecentMessages(30);
        console.group("🔍 Painel de Debug");
        console.log("  Conexões:", info);
        console.log("📊 Estatísticas:", stats);
        console.log("🕒 Mensagens recentes:", recent);
        console.groupEnd();
        this.debugPanelVisible = true;
    }
    hideDebugPanel() {
        this.debugPanelVisible = false;
        console.log("🔍 Painel de debug ocultado");
    }
    showHelpDialog() {
        const helpText = `
🎮 ${CONFIG.SCRIPT_NAME} v${CONFIG.VERSION}

⌨️  Atalhos de Teclado:
F1 - Mostra esta ajuda
F2 - Mostra/oculta painel de debug
F3 - Mostra status da aplicação
F4 - Mostra estado do mundo

🔧 Funcionalidades:
• Interceptação de WebSocket em tempo real
• Sistema de eventos para atualizações do jogo
• Gerenciamento de estado do mundo
• Sincronização entre múltiplas abas
• Monitoramento de latência e conexão

📝 Configurações são salvas automaticamente no localStorage.
		`;
        alert(helpText.trim());
    }
    registerComponent(name, component) {
        this.components.set(name, component);
    }
    getComponent(name) {
        return this.components.get(name);
    }
    destroy() {
        this.components.forEach(component => component.destroy());
        this.components.clear();
        this.isInitialized = false;
    }
}
class ScriptApplication {
    constructor() {
        this.startTime = performance.now();
        this.isInitialized = false;
        this.settingsStore = SettingsStore.getInstance();
        this.uiManager = UIManager.getInstance();
        this.wsInterceptor = WebSocketInterceptor.getInstance();
        this.world = World.getInstance();
        this.setupEventListeners();
    }
    static getInstance() {
        if (!ScriptApplication.instance) {
            ScriptApplication.instance = new ScriptApplication();
        }
        return ScriptApplication.instance;
    }
    setupEventListeners() {
        // Escuta eventos de conexão
        this.wsInterceptor.on(EventType.CONNECTION_STATE_CHANGE, (event) => {
            console.log(`🔄 Estado da conexão mudou: ${event.oldState} → ${event.newState}`, event.reason || "");
        });
        // Escuta eventos de atualização do mundo
        this.wsInterceptor.on(EventType.WORLD_UPDATE, () => {
            this.world.updateCameras();
            this.world.synchronizeViews();
            this.world.cleanupDeadCells();
        });
        // Escuta eventos de posição
        this.wsInterceptor.on(EventType.POSITION_UPDATE, (event) => {
            // Atualiza posição da câmera no mundo
            if (CONFIG.DEFAULT_SETTINGS.enableDebugMode) {
                console.log("📍 Posição atualizada:", event.position, "Escala:", event.scale);
            }
        });
        // Escuta eventos de spawn de células
        this.wsInterceptor.on(EventType.CELL_SPAWN, (event) => {
            if (CONFIG.DEFAULT_SETTINGS.enableDebugMode) {
                console.log("🆕 Nova célula:", event.cellId);
            }
        });
        // Escuta mudanças de estado de conexão
        this.wsInterceptor.on(EventType.CONNECTION_STATE_CHANGE, (event) => {
            if (event.newState === ConnectionState.CONNECTED) {
                console.log("✅ Conectado ao servidor");
            }
            else if (event.newState === ConnectionState.DISCONNECTED) {
                console.log("❌ Desconectado do servidor");
            }
        });
    }
    async initialize() {
        try {
            if (this.isInitialized)
                return true;
            // Aguarda o DOM estar pronto
            if (document.readyState === "loading") {
                await new Promise(resolve => document.addEventListener("DOMContentLoaded", resolve));
            }
            // Inicializa componentes
            this.uiManager.initialize();
            this.settingsStore.setSetting("appVersion", CONFIG.VERSION);
            this.settingsStore.setSetting("lastInitialized", Date.now());
            // Log de inicialização
            console.log(`🚀 ${CONFIG.SCRIPT_NAME} v${CONFIG.VERSION} inicializado`);
            console.log("ℹ️  Pressione F1 para ajuda, F2 para status do WebSocket");
            console.log("🔧 Configurações:", this.settingsStore.getAllSettings());
            this.isInitialized = true;
            return true;
        }
        catch (error) {
            this.lastError = error instanceof Error ? error.message : String(error);
            console.error("❌ Erro na inicialização:", error);
            return false;
        }
    }
    async destroy() {
        try {
            this.wsInterceptor.removeAllListeners();
            this.uiManager.destroy();
            this.isInitialized = false;
            console.log("🛑 Aplicação finalizada");
        }
        catch (error) {
            console.error("❌ Erro ao finalizar aplicação:", error);
        }
    }
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            version: CONFIG.VERSION,
            uptime: performance.now() - this.startTime,
            connectionCount: this.wsInterceptor.getAllConnectionsInfo().length,
            lastError: this.lastError,
        };
    }
}
// ===== INICIALIZAÇÃO AUTOMÁTICA =====
(async () => {
    try {
        const app = ScriptApplication.getInstance();
        await app.initialize();
        // Adiciona handler global para erros não capturados
        window.addEventListener("error", event => {
            console.error("❌ Erro global capturado:", event.error);
        });
        // Adiciona handler para promises rejeitadas
        window.addEventListener("unhandledrejection", event => {
            console.error("❌ Promise rejeitada:", event.reason);
        });
    }
    catch (error) {
        console.error("❌ Falha crítica na inicialização:", error);
    }
})();
