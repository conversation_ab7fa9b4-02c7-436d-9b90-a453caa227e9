// ==UserScript==
// @name         Agario Enhanced Script
// @namespace    agario-enhanced
// @version      2.0.0
// <AUTHOR>
// @description  Script avançado para Agario com interceptação WebSocket e gerenciamento de estado
// @license      MIT
// @match        https://agariobr.com.br/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

/**
 * 🎮 AGARIO ENHANCED SCRIPT v2.0.0 - MODO PASSIVO
 *
 * ⚠️ MODIFICAÇÕES PARA MODO PASSIVO:
 * • REMOVIDO: Envio automático de handshake inicial
 * • REMOVIDO: Pings automáticos para medir latência
 * • REMOVIDO: Quest timers automáticos
 * • REMOVIDO: Reconexão automática
 * • REMOVIDO: Fechamento automático de conexões
 * • MODIFICADO: Interceptação apenas para logging, sem interferir no WebSocket
 *
 * 🔍 FUNCIONALIDADE ATUAL:
 * • Intercepta e monitora mensagens WebSocket (apenas leitura)
 * • Processa e decodifica mensagens do protocolo do jogo
 * • Mantém estado do mundo do jogo atualizado
 * • Emite eventos para outros componentes
 * • NÃO envia nenhuma mensagem automaticamente
 * • NÃO interfere no funcionamento normal do jogo
 *
 * ✨ MELHORIAS IMPLEMENTADAS:
 *
 * 🏗️ ARQUITETURA LIMPA:
 * • Aplicação dos princípios SOLID
 * • Interfaces bem definidas para todos os componentes
 * • Padrão Singleton para gerenciamento de instâncias
 * • Separação clara de responsabilidades
 *
 * 🔄 SISTEMA DE EVENTOS ROBUSTO:
 * • EventEmitter personalizado para comunicação entre componentes
 * • Eventos tipados com TypeScript para maior segurança
 * • Listeners para atualizações do mundo, conexões e células
 * • Tratamento de erros centralizado
 *
 * 🌐 INTERCEPTAÇÃO WEBSOCKET PASSIVA:
 * • Monitoramento em tempo real de todas as conexões (somente leitura)
 * • Processamento inteligente de mensagens do jogo
 * • Decodificação de protocolo sem interferência
 *
 * 🎯 GERENCIAMENTO DE ESTADO:
 * • World class para controle completo do estado do jogo
 * • Sincronização entre múltiplas abas/visualizações
 * • Sistema de câmera avançado com interpolação suave
 * • Limpeza automática de células mortas
 *
 * 💾 ARMAZENAMENTO INTELIGENTE:
 * • StorageService com tratamento de erros
 * • SettingsStore com configurações persistentes
 * • Backup automático de configurações
 *
 * 🎨 INTERFACE DE USUÁRIO:
 * • UIManager com componentes modulares
 * • Sistema de keybinds configurável
 * • Painel de debug interativo
 * • Ajuda contextual integrada
 *
 * 🔧 FUNCIONALIDADES:
 * • F1: Ajuda e documentação
 * • F2: Painel de debug do WebSocket
 * • F3: Status da aplicação
 * • F4: Estado do mundo do jogo
 *
 * 📝 TIPAGEM COMPLETA:
 * • 100% TypeScript sem uso de 'any'
 * • Interfaces seguindo padrão I<Ação><Nome><Escopo>
 * • Tipos readonly para imutabilidade
 * • Enums para constantes do protocolo
 */

// ===== INTERFACES E TIPOS =====
interface IScriptConfig {
	readonly SCRIPT_NAME: string;
	readonly VERSION: string;
	readonly STORAGE_KEYS: {
		readonly SETTINGS: string;
		readonly WORLD_STATE: string;
		readonly NETWORK_STATS: string;
	};
	readonly NETWORK: {
		readonly WEBSOCKET_URL: string;
		readonly PING_INTERVAL: number;
		readonly RECONNECT_DELAY: number;
		readonly MAX_RETRIES: number;
	};
	readonly DEFAULT_SETTINGS: IGameSettings;
}

interface IGameSettings {
	readonly cameraMovement: "default" | "instant";
	readonly camera: "default" | "enhanced";
	readonly multibox: boolean;
	readonly nbox: boolean;
	readonly mergeCamera: boolean;
	readonly cameraSpawnAnimation: boolean;
	readonly cameraSmoothness: number;
	readonly autoZoom: boolean;
	readonly synchronization: "flawless" | "latest" | "disabled";
	readonly drawDelay: number;
	readonly slowerJellyPhysics: boolean;
	readonly enableDebugMode: boolean;
}

// ===== CONFIGURAÇÕES =====
const CONFIG: IScriptConfig = {
	SCRIPT_NAME: "agario-enhanced",
	VERSION: "2.0.0",
	STORAGE_KEYS: {
		SETTINGS: "agario_settings",
		WORLD_STATE: "agario_world_state",
		NETWORK_STATS: "agario_network_stats",
	},
	NETWORK: {
		WEBSOCKET_URL: "servers.agariobr.com.br:4409",
		PING_INTERVAL: 2000,
		RECONNECT_DELAY: 1000,
		MAX_RETRIES: 5,
	},
	DEFAULT_SETTINGS: {
		cameraMovement: "default",
		camera: "default",
		multibox: true,
		nbox: false,
		mergeCamera: true,
		cameraSpawnAnimation: true,
		cameraSmoothness: 10,
		autoZoom: true,
		synchronization: "flawless",
		drawDelay: 120,
		slowerJellyPhysics: false,
		enableDebugMode: false,
	},
} as const;

// ===== ENUMS E TIPOS DE REDE =====
enum NetworkOpcodeEnum {
	WorldUpdate = 0x10, // Atualização completa de células e pellets
	PositionUpdate = 0x11, // Posição e zoom da câmera
	Leaderboard = 0x12, // Top 10 jogadores
	ClearCells = 0x14, // Remove células próprias (morte/respawn)
	RemoveCells = 0x20, // Remove células específicas
	MapBorders = 0x30, // Define limites do mapa
	OwnCells = 0x40, // Informa células próprias
	ChatMessage = 0x63, // Mensagens de chat
	ServerStats = 0x64, // Estatísticas do servidor
	PartyInfo = 0x65, // Código da party
	PartyUpdate = 0x66, // Membros da party
	Minimap = 0x67, // Atualização do minimapa
	PasswordError = 0xb4, // Erro de senha
}

export enum ConnectionState {
	DISCONNECTED = "disconnected",
	CONNECTING = "connecting",
	CONNECTED = "connected",
	RECONNECTING = "reconnecting",
	ERROR = "error",
}

export enum EventType {
	WORLD_UPDATE = "world_update",
	POSITION_UPDATE = "position_update",
	CELL_SPAWN = "cell_spawn",
	CELL_DEATH = "cell_death",
	CONNECTION_STATE_CHANGE = "connection_state_change",
	LEADERBOARD_UPDATE = "leaderboard_update",
	CHAT_MESSAGE = "chat_message",
	ERROR = "error",
}

// ===== INTERFACES DE EVENTOS =====
interface IBaseEvent {
	readonly type: EventType;
	readonly timestamp: number;
	readonly viewId: symbol;
}

interface IWorldUpdateEvent extends IBaseEvent {
	readonly type: EventType.WORLD_UPDATE;
	readonly cells: ReadonlyMap<number, ICellData>;
	readonly pellets: ReadonlyMap<number, ICellData>;
}

interface IPositionUpdateEvent extends IBaseEvent {
	readonly type: EventType.POSITION_UPDATE;
	readonly position: IPosition;
	readonly scale: number;
}

interface ICellSpawnEvent extends IBaseEvent {
	readonly type: EventType.CELL_SPAWN;
	readonly cellId: number;
	readonly position: IPosition;
	readonly radius: number;
}

interface ICellDeathEvent extends IBaseEvent {
	readonly type: EventType.CELL_DEATH;
	readonly cellId: number;
	readonly killerId?: number;
}

interface IConnectionStateChangeEvent extends IBaseEvent {
	readonly type: EventType.CONNECTION_STATE_CHANGE;
	readonly oldState: ConnectionState;
	readonly newState: ConnectionState;
	readonly reason?: string;
}

type GameEvent = IWorldUpdateEvent | IPositionUpdateEvent | ICellSpawnEvent | ICellDeathEvent | IConnectionStateChangeEvent;

// ===== INTERFACES DE DADOS =====
interface IPosition {
	readonly x: number;
	readonly y: number;
}

interface ICellData {
	readonly id: number;
	readonly position: IPosition;
	readonly radius: number;
	readonly mass: number;
	readonly color?: string;
	readonly name?: string;
	readonly isOwned: boolean;
	readonly isAlive: boolean;
	readonly lastUpdate: number;
}

// ===== SISTEMA DE EVENTOS =====
interface IEventListener<T extends IBaseEvent = IBaseEvent> {
	(event: T): void;
}

interface IEventEmitter {
	on<T extends IBaseEvent>(eventType: EventType, listener: IEventListener<T>): void;
	off<T extends IBaseEvent>(eventType: EventType, listener: IEventListener<T>): void;
	emit<T extends IBaseEvent>(event: T): void;
	removeAllListeners(eventType?: EventType): void;
}

class EventEmitter implements IEventEmitter {
	private readonly listeners = new Map<EventType, Set<IEventListener>>();

	on<T extends IBaseEvent>(eventType: EventType, listener: IEventListener<T>): void {
		if (!this.listeners.has(eventType)) {
			this.listeners.set(eventType, new Set());
		}
		this.listeners.get(eventType)!.add(listener as IEventListener);
	}

	off<T extends IBaseEvent>(eventType: EventType, listener: IEventListener<T>): void {
		const eventListeners = this.listeners.get(eventType);
		if (eventListeners) {
			eventListeners.delete(listener as IEventListener);
		}
	}

	emit<T extends IBaseEvent>(event: T): void {
		const eventListeners = this.listeners.get(event.type);
		if (eventListeners) {
			eventListeners.forEach(listener => {
				try {
					listener(event);
				} catch (error) {
					console.error(`Erro ao executar listener para evento ${event.type}:`, error);
				}
			});
		}
	}

	removeAllListeners(eventType?: EventType): void {
		if (eventType) {
			this.listeners.delete(eventType);
		} else {
			this.listeners.clear();
		}
	}
}

// ===== SERVIÇOS DE ARMAZENAMENTO =====
interface IStorageService {
	get<T>(key: string): T | null;
	set<T>(key: string, value: T): boolean;
	remove(key: string): boolean;
	clear(): boolean;
	exists(key: string): boolean;
}

class StorageService implements IStorageService {
	private static instance: StorageService;

	private constructor() {}

	static getInstance(): StorageService {
		if (!StorageService.instance) {
			StorageService.instance = new StorageService();
		}
		return StorageService.instance;
	}

	get<T>(key: string): T | null {
		try {
			const item = localStorage.getItem(key);
			return item ? JSON.parse(item) : null;
		} catch (error) {
			console.error(`Erro ao recuperar item do storage: ${key}`, error);
			return null;
		}
	}

	set<T>(key: string, value: T): boolean {
		try {
			localStorage.setItem(key, JSON.stringify(value));
			return true;
		} catch (error) {
			console.error(`Erro ao salvar item no storage: ${key}`, error);
			return false;
		}
	}

	remove(key: string): boolean {
		try {
			localStorage.removeItem(key);
			return true;
		} catch (error) {
			console.error(`Erro ao remover item do storage: ${key}`, error);
			return false;
		}
	}

	clear(): boolean {
		try {
			localStorage.clear();
			return true;
		} catch (error) {
			console.error("Erro ao limpar storage", error);
			return false;
		}
	}

	exists(key: string): boolean {
		return localStorage.getItem(key) !== null;
	}
}

// ===== UTILITÁRIOS DOM =====
interface IElementOptions {
	readonly className?: string;
	readonly textContent?: string;
	readonly styles?: Partial<CSSStyleDeclaration>;
	readonly attributes?: Record<string, string>;
	readonly eventListeners?: Record<string, EventListener>;
}

interface IDOMUtilities {
	createElement<T extends HTMLElement>(tag: string, options?: IElementOptions): T;
	removeElement(element: HTMLElement | null): boolean;
	findElement(selector: string): HTMLElement | null;
	findElements(selector: string): HTMLElement[];
}

class DOMUtilities implements IDOMUtilities {
	private static instance: DOMUtilities;

	private constructor() {}

	static getInstance(): DOMUtilities {
		if (!DOMUtilities.instance) {
			DOMUtilities.instance = new DOMUtilities();
		}
		return DOMUtilities.instance;
	}

	createElement<T extends HTMLElement>(tag: string, options: IElementOptions = {}): T {
		const element = document.createElement(tag) as T;

		if (options.className) element.className = options.className;
		if (options.textContent) element.textContent = options.textContent;
		if (options.styles) Object.assign(element.style, options.styles);

		if (options.attributes) {
			Object.entries(options.attributes).forEach(([key, value]) => {
				element.setAttribute(key, value);
			});
		}

		if (options.eventListeners) {
			Object.entries(options.eventListeners).forEach(([event, listener]) => {
				element.addEventListener(event, listener);
			});
		}

		return element;
	}

	removeElement(element: HTMLElement | null): boolean {
		if (element?.parentNode) {
			element.parentNode.removeChild(element);
			return true;
		}
		return false;
	}

	findElement(selector: string): HTMLElement | null {
		return document.querySelector(selector);
	}

	findElements(selector: string): HTMLElement[] {
		return Array.from(document.querySelectorAll(selector));
	}
}

// ===== GERENCIAMENTO DE ESTADO =====
type SettingsValue = string | number | boolean | object | null;

interface ISettingsStore {
	getAllSettings(): Record<string, SettingsValue>;
	getSetting<T extends SettingsValue>(key: string): T | null;
	setSetting(key: string, value: SettingsValue): boolean;
	resetToDefaults(): boolean;
}

class SettingsStore implements ISettingsStore {
	private static instance: SettingsStore;
	private settings: Record<string, SettingsValue> = {};
	private readonly storageKey = CONFIG.STORAGE_KEYS.SETTINGS;
	private readonly storageService: IStorageService;

	private constructor() {
		this.storageService = StorageService.getInstance();
		this.loadSettings();
	}

	static getInstance(): SettingsStore {
		if (!SettingsStore.instance) {
			SettingsStore.instance = new SettingsStore();
		}
		return SettingsStore.instance;
	}

	private loadSettings(): void {
		const savedSettings = this.storageService.get<Record<string, SettingsValue>>(this.storageKey);
		if (savedSettings && typeof savedSettings === "object") {
			this.settings = { ...CONFIG.DEFAULT_SETTINGS, ...savedSettings };
		} else {
			this.settings = { ...CONFIG.DEFAULT_SETTINGS };
		}
	}

	private saveSettings(): boolean {
		return this.storageService.set(this.storageKey, this.settings);
	}

	getAllSettings(): Record<string, SettingsValue> {
		return { ...this.settings };
	}

	getSetting<T extends SettingsValue>(key: string): T | null {
		return (this.settings[key] as T) || null;
	}

	setSetting(key: string, value: SettingsValue): boolean {
		this.settings[key] = value;
		return this.saveSettings();
	}

	resetToDefaults(): boolean {
		this.settings = { ...CONFIG.DEFAULT_SETTINGS };
		return this.saveSettings();
	}
}

// ===== GERENCIADOR DE KEYBINDS ESSENCIAL =====
type KeyHandler = (event: KeyboardEvent) => void | boolean;
interface KeyBinding {
	key: string;
	ctrl?: boolean;
	shift?: boolean;
	alt?: boolean;
	handler: KeyHandler;
	description?: string;
}
class KeyBindManager {
	private static instance: KeyBindManager;
	private keyBindings: Map<string, KeyBinding> = new Map();

	private constructor() {
		this.setupGlobalListener();
	}

	static getInstance(): KeyBindManager {
		if (!KeyBindManager.instance) {
			KeyBindManager.instance = new KeyBindManager();
		}
		return KeyBindManager.instance;
	}

	private setupGlobalListener(): void {
		document.addEventListener("keydown", event => {
			const key = event.key.toLowerCase();
			const binding = this.keyBindings.get(key);
			if (binding) {
				const result = binding.handler(event);
				if (result !== false) {
					event.preventDefault();
					event.stopPropagation();
				}
			}
		});
	}

	register(binding: KeyBinding): boolean {
		const key = binding.key.toLowerCase();
		this.keyBindings.set(key, binding);
		return true;
	}

	listBindings(): KeyBinding[] {
		return Array.from(this.keyBindings.values());
	}
}

// ===== INTERCEPTADOR DE WEBSOCKET =====

// ===== NETWORK TYPES & INTERFACES =====

/** Handshake data for message encryption/decryption */
interface NetworkHandshake {
	encryptTable: Uint8Array;
	decryptTable: Uint8Array;
}

/** Game state blocking information (entering/leaving world) */
interface PlayBlockState {
	state: "leaving" | "joining";
	startedAt: number;
}

/** Individual connection state for each game view */
interface NetworkConnection {
	handshake?: NetworkHandshake;
	websocket?: WebSocket;
	latency?: number;
	lastPingTime?: number;
	playBlock?: PlayBlockState;
	rejectionCount: number;
	retryCount: number;
}

/** Message log entry for debugging and analysis */
interface MessageLogEntry {
	timestamp: number;
	viewId: symbol;
	data: any;
	direction: "sent" | "received";
	opcode?: NetworkOpcodeEnum;
	isDecoded?: boolean;
}
// ===== INTERCEPTADOR DE WEBSOCKET =====
interface IWebSocketInterceptor extends IEventEmitter {
	getAllConnectionsInfo(): IConnectionInfo[];
	getRecentMessages(maxAgeSeconds?: number): MessageLogEntry[];
	getNetworkStats(): INetworkStats;
	sendQuestTime(viewId: symbol): void;
}

interface IConnectionInfo {
	readonly viewId: symbol;
	readonly url?: string;
	readonly readyState?: number;
	readonly latency?: number;
	readonly rejectionCount: number;
	readonly retryCount: number;
	readonly state: ConnectionState;
}

interface INetworkStats {
	readonly totalConnections: number;
	readonly activeConnections: number;
	readonly totalMessages: number;
	readonly averageLatency: number;
	readonly uptime: number;
}

class WebSocketInterceptor extends EventEmitter implements IWebSocketInterceptor {
	private static instance: WebSocketInterceptor;

	// Gerenciamento principal do WebSocket
	private readonly originalWebSocket: typeof WebSocket;
	private readonly textEncoder = new TextEncoder();
	private readonly startTime = performance.now();

	// Gerenciamento do estado da conexão
	private readonly connections = new Map<symbol, NetworkConnection>();
	private readonly messageLog: MessageLogEntry[] = [];
	private readonly connectionStates = new Map<symbol, ConnectionState>();

	// Constantes de configuração
	private static readonly PING_INTERVAL = CONFIG.NETWORK.PING_INTERVAL;
	private static readonly GAMEMODE_CHECK_INTERVAL = 200;
	private static readonly QUEST_TIMER_INTERVAL = 1000;
	private static readonly MESSAGE_BUFFER_LIMIT = 1000;
	private static readonly MESSAGE_BUFFER_TRIM_SIZE = 500;

	private constructor() {
		super();
		this.originalWebSocket = window.WebSocket;
		this.setupWebSocketInterception();
		this.startPeriodicTasks();
	}

	// ===== PADRÃO SINGLETON =====

	static getInstance(): WebSocketInterceptor {
		if (!WebSocketInterceptor.instance) {
			WebSocketInterceptor.instance = new WebSocketInterceptor();
		}
		return WebSocketInterceptor.instance;
	}

	// ===== CONFIGURAÇÃO DA INTERCEPTAÇÃO DO WEBSOCKET =====

	/** Intercepta o construtor do WebSocket para monitorar conexões com o servidor alvo */
	private setupWebSocketInterception(): void {
		const self = this;

		window.WebSocket = class extends self.originalWebSocket {
			constructor(url: string | URL, protocols?: string | string[]) {
				super(url, protocols);
				const urlString = url.toString();

				if (urlString.includes(CONFIG.NETWORK.WEBSOCKET_URL)) {
					self.handleNewConnection(this, urlString);
				}
			}
		};

		// Preserva a cadeia de protótipos original do WebSocket
		Object.setPrototypeOf(window.WebSocket.prototype, this.originalWebSocket.prototype);
		Object.setPrototypeOf(window.WebSocket, this.originalWebSocket);
	}

	/** Lida com a detecção de uma nova conexão WebSocket */
	private handleNewConnection(socket: WebSocket, url: string): void {
		console.log("🔗 Nova conexão WebSocket detectada:", url);

		const viewId = this.findOrCreateViewId();
		const connection = this.getOrCreateConnection(viewId);
		connection.websocket = socket;

		this.attachEventListeners(socket, viewId);
	}

	// ===== GERENCIAMENTO DE CONEXÃO =====

	/** Obtém ou cria uma conexão para a visualização especificada */
	private getOrCreateConnection(viewId: symbol): NetworkConnection {
		if (this.connections.has(viewId)) {
			return this.connections.get(viewId)!;
		}

		const connection: NetworkConnection = {
			handshake: undefined,
			websocket: undefined,
			latency: undefined,
			lastPingTime: undefined,
			playBlock: undefined,
			rejectionCount: 0,
			retryCount: 3,
		};

		this.connections.set(viewId, connection);
		return connection;
	}

	/** REMOVIDO: Não cria conexões automaticamente - apenas observa as existentes */
	private createConnection(viewId: symbol): WebSocket | undefined {
		// REMOVIDO: Não cria novas conexões - apenas monitora as existentes
		console.log("🔍 Tentativa de criar conexão detectada para viewId:", viewId.toString());
		return undefined;
	}

	// ===== OUVINTES DE EVENTOS =====

	/** Anexa ouvintes de eventos à instância do WebSocket */
	private attachEventListeners(socket: WebSocket, viewId: symbol): void {
		const originalSend = socket.send.bind(socket);

		// Intercepta mensagens enviadas APENAS para logging - NÃO modifica o comportamento
		socket.send = (data: string | ArrayBufferLike | Blob | ArrayBufferView) => {
			this.logMessage(viewId, data, "sent");
			return originalSend(data);
		};

		// Anexa manipuladores de eventos do WebSocket
		socket.addEventListener("open", () => this.handleConnectionOpen(socket, viewId));
		socket.addEventListener("message", event => this.handleMessage(event, viewId));
		socket.addEventListener("error", event => this.handleConnectionError(event, viewId));
		socket.addEventListener("close", event => this.handleConnectionClose(event, viewId));
	}

	// ===== MANIPULADORES DE EVENTOS =====

	/** Lida com o evento de abertura da conexão WebSocket */
	private handleConnectionOpen(socket: WebSocket, viewId: symbol): void {
		console.log("✅ WebSocket conectado:", socket.url);

		const connection = this.connections.get(viewId);
		if (!connection) {
			return; // NÃO fecha a conexão - apenas não monitora
		}

		// Reseta contadores de erro em conexão bem-sucedida
		connection.rejectionCount = 0;
		connection.retryCount = 3;

		// REMOVIDO: Não envia handshake inicial - apenas observa
		// socket.send(this.textEncoder.encode(""));
	}

	/** Lida com mensagens recebidas do WebSocket */
	private handleMessage(event: MessageEvent, viewId: symbol): void {
		const connection = this.connections.get(viewId);
		if (!connection) return;

		const dataView = new DataView(event.data);
		this.logMessage(viewId, event.data, "received");

		// Processa handshake se ainda não estabelecido
		if (!connection.handshake) {
			this.processHandshake(dataView, connection);
			return;
		}

		// Decodifica o opcode da mensagem e processa
		const rawOpcode = dataView.getUint8(0);
		const decodedOpcode = connection.handshake.decryptTable[rawOpcode];
		dataView.setUint8(0, decodedOpcode);

		this.processGameMessage(dataView, decodedOpcode, viewId, connection);
	}

	/** Lida com erros de conexão WebSocket */
	private handleConnectionError(event: Event, viewId: symbol): void {
		console.error("💥 Erro no WebSocket:", event);
		this.updateConnectionState(viewId, ConnectionState.ERROR, "WebSocket error occurred");
	}

	/** Lida com o fechamento da conexão WebSocket */
	private handleConnectionClose(event: CloseEvent, viewId: symbol): void {
		console.log("🔌 WebSocket fechado:", event.code, event.reason);

		const connection = this.connections.get(viewId);
		if (!connection) return;

		this.resetConnectionState(connection);
		// REMOVIDO: Não tenta reconectar automaticamente - apenas observa
		// this.attemptReconnection(viewId, connection);
	}

	// ===== PROCESSAMENTO DE MENSAGENS =====

	/** Processa a mensagem inicial de handshake para estabelecer tabelas de criptografia */
	private processHandshake(dataView: DataView, connection: NetworkConnection): void {
		const HANDSHAKE_OFFSET = 10;
		const TABLE_SIZE = 256;

		const encryptTable = new Uint8Array(TABLE_SIZE);
		const decryptTable = new Uint8Array(TABLE_SIZE);

		// Constrói tabelas de lookup de criptografia/descriptografia
		for (let i = 0; i < TABLE_SIZE; i++) {
			const encryptedValue = dataView.getUint8(HANDSHAKE_OFFSET + i);
			encryptTable[i] = encryptedValue;
			decryptTable[encryptedValue] = i;
		}

		connection.handshake = { encryptTable, decryptTable };
		console.log("🤝 Handshake estabelecido com sucesso");
	}

	/** Direciona mensagens do jogo decodificadas com base no opcode */
	private processGameMessage(dataView: DataView, opcode: number, viewId: symbol, connection: NetworkConnection): void {
		const timestamp = performance.now();

		switch (opcode) {
			case NetworkOpcodeEnum.WorldUpdate:
				this.processWorldUpdate(dataView, viewId, timestamp);
				break;

			case NetworkOpcodeEnum.PositionUpdate:
				this.processPositionUpdate(dataView, viewId);
				break;

			case NetworkOpcodeEnum.Leaderboard:
				this.processLeaderboard(dataView, viewId);
				break;

			case NetworkOpcodeEnum.ClearCells:
				this.processClearCells(dataView, viewId);
				break;

			case NetworkOpcodeEnum.RemoveCells:
				this.processRemoveCells(dataView, viewId);
				break;

			case NetworkOpcodeEnum.MapBorders:
				this.processMapBorders(dataView, viewId);
				break;

			case NetworkOpcodeEnum.OwnCells:
				this.processOwnCells(dataView, viewId, timestamp);
				break;

			case NetworkOpcodeEnum.ChatMessage:
				this.processChatMessage(dataView, viewId);
				break;

			case NetworkOpcodeEnum.ServerStats:
				this.processServerStats(dataView, viewId, connection, timestamp);
				break;

			case NetworkOpcodeEnum.PasswordError:
				this.processPasswordError();
				break;

			default:
				console.log(`❓ Opcode desconhecido: 0x${opcode.toString(16)}`);
		}
	}

	// ===== PROCESSADORES DE MENSAGENS DO JOGO =====

	private processWorldUpdate(dataView: DataView, viewId: symbol, timestamp: number): void {
		try {
			const world = World.getInstance();
			const vision = world.createVision(viewId);
			const cells = new Map<number, ICellData>();
			const pellets = new Map<number, ICellData>();

			let offset = 1; // Pula o opcode

			// Lê células removidas
			const removedCellsCount = dataView.getUint16(offset, true);
			offset += 2;

			for (let i = 0; i < removedCellsCount; i++) {
				const cellId = dataView.getUint32(offset, true);
				offset += 4;
				world.removeCell(cellId, viewId, timestamp);
			}

			// Lê células atualizadas/novas
			while (offset < dataView.byteLength) {
				const cellId = dataView.getUint32(offset, true);
				offset += 4;

				if (cellId === 0) break; // Fim das células

				const x = dataView.getFloat32(offset, true);
				offset += 4;
				const y = dataView.getFloat32(offset, true);
				offset += 4;
				const radius = dataView.getFloat32(offset, true);
				offset += 4;

				// Lê cor (3 bytes RGB)
				const r = dataView.getUint8(offset++);
				const g = dataView.getUint8(offset++);
				const b = dataView.getUint8(offset++);
				const color = `rgb(${r},${g},${b})`;

				// Lê flags
				const flags = dataView.getUint8(offset++);
				const isVirus = (flags & 1) !== 0;
				const isFood = (flags & 2) !== 0;
				const isEjected = (flags & 4) !== 0;

				// Lê nome se presente
				let name = "";
				if (!isFood && !isVirus) {
					const nameLength = dataView.getUint16(offset, true);
					offset += 2;
					if (nameLength > 0) {
						const nameBytes = new Uint16Array(dataView.buffer, offset, nameLength / 2);
						name = String.fromCharCode(...nameBytes);
						offset += nameLength;
					}
				}

				const cellData: ICellData = {
					id: cellId,
					position: { x, y },
					radius,
					mass: (radius * radius) / 100,
					color,
					name,
					isOwned: vision.owned.has(cellId),
					isAlive: true,
					lastUpdate: timestamp,
				};

				if (isFood) {
					pellets.set(cellId, cellData);
					world.updatePellet(cellId, cellData, viewId, timestamp);
				} else {
					cells.set(cellId, cellData);
					world.updateCell(cellId, cellData, viewId, timestamp);
				}
			}

			// Emite evento de atualização do mundo
			this.emit<IWorldUpdateEvent>({
				type: EventType.WORLD_UPDATE,
				timestamp,
				viewId,
				cells,
				pellets,
			});

			if (CONFIG.DEFAULT_SETTINGS.enableDebugMode) {
				console.log("🌍 Mundo atualizado:", {
					cellCount: cells.size,
					pelletCount: pellets.size,
					removedCount: removedCellsCount,
				});
			}
		} catch (error) {
			console.error("Erro ao processar atualização do mundo:", error);
		}
	}

	private processPositionUpdate(dataView: DataView, viewId: symbol): void {
		try {
			const x = dataView.getFloat32(1, true);
			const y = dataView.getFloat32(5, true);
			const scale = dataView.getFloat32(9, true);
			const timestamp = performance.now();

			// Atualiza posição da câmera no mundo
			const world = World.getInstance();
			world.updateCameraPosition(viewId, x, y, scale, timestamp);

			this.emit<IPositionUpdateEvent>({
				type: EventType.POSITION_UPDATE,
				timestamp,
				viewId,
				position: { x, y },
				scale,
			});

			if (CONFIG.DEFAULT_SETTINGS.enableDebugMode) {
				console.log(`📍 Posição da câmera: (${x.toFixed(2)}, ${y.toFixed(2)}) escala: ${scale.toFixed(3)}`);
			}
		} catch (error) {
			console.error("Erro ao processar atualização de posição:", error);
		}
	}

	private processLeaderboard(dataView: DataView, viewId: symbol): void {
		try {
			const world = World.getInstance();
			const leaderboard: any[] = [];
			let offset = 1; // Pula o opcode

			// Lê número de entradas no leaderboard
			const count = dataView.getUint32(offset, true);
			offset += 4;

			for (let i = 0; i < count && i < 10; i++) {
				// Lê ID da célula (se aplicável)
				const cellId = dataView.getUint32(offset, true);
				offset += 4;

				// Lê nome do jogador
				const nameLength = dataView.getUint16(offset, true);
				offset += 2;

				let name = "";
				if (nameLength > 0) {
					const nameBytes = new Uint16Array(dataView.buffer, offset, nameLength / 2);
					name = String.fromCharCode(...nameBytes);
					offset += nameLength;
				}

				leaderboard.push({ position: i + 1, cellId, name });
			}

			// Atualiza leaderboard no mundo
			world.updateLeaderboard(viewId, leaderboard, performance.now());

			if (CONFIG.DEFAULT_SETTINGS.enableDebugMode) {
				console.log("🏆 Leaderboard atualizado:", leaderboard);
			}
		} catch (error) {
			console.error("Erro ao processar leaderboard:", error);
		}
	}

	private processClearCells(dataView: DataView, viewId: symbol): void {
		try {
			const world = World.getInstance();
			const vision = world.createVision(viewId);

			// Remove todas as células próprias
			const ownedCells = Array.from(vision.owned);
			for (const cellId of ownedCells) {
				world.removeOwnCell(cellId, viewId);
			}

			if (CONFIG.DEFAULT_SETTINGS.enableDebugMode) {
				console.log(`🧹 ${ownedCells.length} células próprias removidas`);
			}
		} catch (error) {
			console.error("Erro ao processar limpeza de células:", error);
		}
	}

	private processRemoveCells(dataView: DataView, viewId: symbol): void {
		try {
			const world = World.getInstance();
			const timestamp = performance.now();
			let offset = 1; // Pula o opcode

			// Lê número de células a remover
			const count = dataView.getUint16(offset, true);
			offset += 2;

			for (let i = 0; i < count; i++) {
				const cellId = dataView.getUint32(offset, true);
				offset += 4;
				world.removeCell(cellId, viewId, timestamp);
			}

			if (CONFIG.DEFAULT_SETTINGS.enableDebugMode) {
				console.log(`❌ ${count} células removidas`);
			}
		} catch (error) {
			console.error("Erro ao processar remoção de células:", error);
		}
	}

	private processMapBorders(dataView: DataView, viewId: symbol): void {
		try {
			const left = dataView.getFloat64(1, true);
			const top = dataView.getFloat64(9, true);
			const right = dataView.getFloat64(17, true);
			const bottom = dataView.getFloat64(25, true);

			// Atualiza limites do mapa no mundo
			const world = World.getInstance();
			world.updateMapBorders(viewId, left, top, right, bottom);

			if (CONFIG.DEFAULT_SETTINGS.enableDebugMode) {
				console.log(`🗺️ Limites do mapa - L:${left} T:${top} R:${right} B:${bottom}`);
			}
		} catch (error) {
			console.error("Erro ao processar limites do mapa:", error);
		}
	}

	private processOwnCells(dataView: DataView, viewId: symbol, timestamp: number): void {
		try {
			const world = World.getInstance();
			let offset = 1; // Pula o opcode

			// Lê número de células próprias
			const count = dataView.getUint32(offset, true);
			offset += 4;

			for (let i = 0; i < count; i++) {
				const cellId = dataView.getUint32(offset, true);
				offset += 4;

				// Adiciona célula própria ao mundo
				world.addOwnCell(cellId, viewId, timestamp);

				this.emit<ICellSpawnEvent>({
					type: EventType.CELL_SPAWN,
					timestamp,
					viewId,
					cellId,
					position: { x: 0, y: 0 }, // Será atualizado na próxima atualização do mundo
					radius: 0, // Será atualizado na próxima atualização do mundo
				});
			}

			if (CONFIG.DEFAULT_SETTINGS.enableDebugMode) {
				console.log(`👤 ${count} células próprias adicionadas`);
			}
		} catch (error) {
			console.error("Erro ao processar células próprias:", error);
		}
	}

	private processChatMessage(dataView: DataView, viewId: symbol): void {
		try {
			let offset = 1; // Pula o opcode

			// Lê flags da mensagem
			const flags = dataView.getUint8(offset++);
			const isServerMessage = (flags & 0x80) !== 0;
			const isAdminMessage = (flags & 0x40) !== 0;

			// Lê cor da mensagem (se aplicável)
			let color = "#FFFFFF";
			if (!isServerMessage) {
				const r = dataView.getUint8(offset++);
				const g = dataView.getUint8(offset++);
				const b = dataView.getUint8(offset++);
				color = `rgb(${r},${g},${b})`;
			}

			// Lê nome do remetente
			const nameLength = dataView.getUint16(offset, true);
			offset += 2;

			let senderName = "";
			if (nameLength > 0) {
				const nameBytes = new Uint16Array(dataView.buffer, offset, nameLength / 2);
				senderName = String.fromCharCode(...nameBytes);
				offset += nameLength;
			}

			// Lê mensagem
			const messageLength = dataView.getUint16(offset, true);
			offset += 2;

			let message = "";
			if (messageLength > 0) {
				const messageBytes = new Uint16Array(dataView.buffer, offset, messageLength / 2);
				message = String.fromCharCode(...messageBytes);
			}

			if (CONFIG.DEFAULT_SETTINGS.enableDebugMode) {
				console.log(`💬 Chat [${senderName}]: ${message}`, {
					isServerMessage,
					isAdminMessage,
					color,
				});
			}
		} catch (error) {
			console.error("Erro ao processar mensagem de chat:", error);
		}
	}

	private processServerStats(dataView: DataView, viewId: symbol, connection: NetworkConnection, timestamp: number): void {
		try {
			let offset = 1; // Pula o opcode

			// Lê estatísticas do servidor
			const playersOnline = dataView.getUint16(offset, true);
			offset += 2;

			const playersLimit = dataView.getUint16(offset, true);
			offset += 2;

			// Calcula latência se o ping foi enviado
			if (connection.lastPingTime !== undefined) {
				connection.latency = timestamp - connection.lastPingTime;
				if (CONFIG.DEFAULT_SETTINGS.enableDebugMode) {
					console.log(`🏓 Latência: ${connection.latency.toFixed(1)}ms | Jogadores: ${playersOnline}/${playersLimit}`);
				}
			}
			connection.lastPingTime = undefined;

			// Atualiza estatísticas no mundo
			const world = World.getInstance();
			const vision = world.createVision(viewId);
			vision.stats = {
				playersOnline,
				playersLimit,
				latency: connection.latency || 0,
				timestamp,
			};
		} catch (error) {
			console.error("Erro ao processar estatísticas do servidor:", error);
		}
	}

	private processPasswordError(): void {
		console.error("🔒 Falha na autenticação da senha!");
		// Implementar lógica de nova tentativa de senha se necessário
	}

	// ===== GERENCIAMENTO DO ESTADO DA CONEXÃO =====

	/** Reseta o estado da conexão após desconexão */
	private resetConnectionState(connection: NetworkConnection): void {
		connection.handshake = undefined;
		connection.latency = undefined;
		connection.lastPingTime = undefined;
		connection.playBlock = undefined;
		connection.rejectionCount++;
		connection.websocket = undefined;

		if (connection.retryCount > 0) {
			connection.retryCount--;
		}
	}

	/** Tenta reconectar com estratégia de backoff */
	private attemptReconnection(viewId: symbol, connection: NetworkConnection): void {
		const baseDelay = 500;
		const longDelay = 5000;
		const maxRejections = 5;

		if (connection.retryCount > 0) {
			setTimeout(() => this.createConnection(viewId), baseDelay);
		} else {
			// Reseta o contador de tentativas e usa delay maior se houver muitas rejeições
			connection.retryCount = 3;
			const delay = connection.rejectionCount >= maxRejections ? longDelay : baseDelay;
			setTimeout(() => this.createConnection(viewId), delay);
		}
	}

	// ===== TAREFAS PERIÓDICAS =====

	/** Inicia tarefas periódicas para monitoramento e atualizações */
	private startPeriodicTasks(): void {
		// REMOVIDO: Não envia pings automáticos
		// this.startPingMonitoring();
		this.startConnectionMonitoring();
		// REMOVIDO: Não envia quest timers automáticos
		// this.startQuestTimers();
	}

	/** Envia mensagens periodicas de ping para medir latência */
	private startPingMonitoring(): void {
		setInterval(() => {
			for (const connection of this.connections.values()) {
				if (!this.isConnectionReady(connection)) continue;
				// marca a conexão como não responsiva se o ping anterior não foi respondido
				if (connection.lastPingTime !== undefined) connection.latency = -1;
				connection.lastPingTime = performance.now();
				this.sendPing(connection);
			}
		}, WebSocketInterceptor.PING_INTERVAL);
	}

	/** Monitora as conexões WebSocket para garantir que estão ativas e correspondem ao jogo atual */
	private startConnectionMonitoring(): void {
		setInterval(() => {
			const currentGameUrl = this.getCurrentGameUrl();

			for (const connection of this.connections.values()) {
				if (!connection.websocket) continue;
				if (!this.isConnectionActive(connection.websocket)) continue;
				if (connection.websocket.url === currentGameUrl) continue;

				// REMOVIDO: Não fecha conexões automaticamente - apenas monitora
				// connection.websocket.close();
				console.log("🔍 Conexão detectada com URL diferente:", connection.websocket.url);
			}
		}, WebSocketInterceptor.GAMEMODE_CHECK_INTERVAL);
	}

	/** Envia periodicamente atualizações de tempo da missão */
	private startQuestTimers(): void {
		setInterval(() => {
			for (const viewId of this.connections.keys()) {
				this.sendQuestTime(viewId);
			}
		}, WebSocketInterceptor.QUEST_TIMER_INTERVAL);
	}

	// ===== UTILITY METHODS =====

	/** Verifica se a conexão está pronta para comunicação */
	private isConnectionReady(connection: NetworkConnection): boolean {
		return !!(connection.handshake && connection.websocket?.readyState === WebSocket.OPEN);
	}

	/** Verifica se a conexão WebSocket está ativa */
	private isConnectionActive(websocket: WebSocket): boolean {
		return websocket.readyState === WebSocket.CONNECTING || websocket.readyState === WebSocket.OPEN;
	}

	/** REMOVIDO: Não envia pings - apenas observa */
	private sendPing(connection: NetworkConnection): void {
		// REMOVIDO: Não envia pings automáticos
		console.log("🔍 Ping seria enviado para conexão (BLOQUEADO)");
		return;
	}

	/** REMOVIDO: Não envia opcodes - apenas observa */
	private sendOpcode(viewId: symbol, opcode: number): void {
		// REMOVIDO: Não envia opcodes automáticos
		console.log(`🔍 Opcode ${opcode} seria enviado para viewId (BLOQUEADO)`);
		return;
	}

	/** Logs message for debugging and analysis */
	private logMessage(viewId: symbol, data: any, direction: "sent" | "received"): void {
		this.messageLog.push({
			timestamp: performance.now(),
			viewId,
			data,
			direction,
		});

		// Limite de tamanho do buffer de mensagens
		if (this.messageLog.length > WebSocketInterceptor.MESSAGE_BUFFER_LIMIT) {
			this.messageLog.splice(0, this.messageLog.length - WebSocketInterceptor.MESSAGE_BUFFER_TRIM_SIZE);
		}
	}

	/** Busca ou cria um identificador de visualização */
	private findOrCreateViewId(): symbol {
		// TODO: Implementar gerenciamento de visualizações baseado em abas do jogo
		return Symbol("primary-view");
	}

	/** Retorna a URL do servidor de jogo atual */
	private getCurrentGameUrl(): string {
		return `ws://${CONFIG.NETWORK.WEBSOCKET_URL}`;
	}

	/** Exibe mensagem de erro de conexão */
	private showConnectionError(): void {
		console.error("❌ Connection failed: Check server address or security settings");
	}

	// ===== PUBLIC API =====

	/** REMOVIDO: Não envia quest time - apenas observa */
	public sendQuestTime(viewId: symbol): void {
		// REMOVIDO: Não envia quest time automático
		console.log("🔍 Quest time seria enviado (BLOQUEADO)");
		return;
	}

	/** Obtém informações sobre todas as conexões ativas */
	public getAllConnectionsInfo(): IConnectionInfo[] {
		return Array.from(this.connections.entries()).map(([viewId, connection]) => ({
			viewId,
			url: connection.websocket?.url,
			readyState: connection.websocket?.readyState,
			latency: connection.latency,
			rejectionCount: connection.rejectionCount,
			retryCount: connection.retryCount,
			state: this.getConnectionState(viewId),
		}));
	}

	/** Obtém as mensagens recentes do log */
	public getRecentMessages(maxAgeSeconds: number = 10): MessageLogEntry[] {
		const cutoffTime = performance.now() - maxAgeSeconds * 1000;
		return this.messageLog.filter(entry => entry.timestamp > cutoffTime);
	}

	/** Obtém informações de estatísticas de rede */
	public getNetworkStats(): INetworkStats {
		const connections = Array.from(this.connections.values());
		const activeConnections = connections.filter(c => c.websocket?.readyState === WebSocket.OPEN);
		const validLatencies = connections.map(c => c.latency).filter((lat): lat is number => lat !== undefined && lat > 0);

		return {
			totalConnections: this.connections.size,
			activeConnections: activeConnections.length,
			totalMessages: this.messageLog.length,
			averageLatency: validLatencies.length > 0 ? validLatencies.reduce((sum, lat) => sum + lat, 0) / validLatencies.length : 0,
			uptime: performance.now() - this.startTime,
		};
	}

	/** Obtém o estado atual da conexão */
	private getConnectionState(viewId: symbol): ConnectionState {
		const connection = this.connections.get(viewId);
		if (!connection?.websocket) return ConnectionState.DISCONNECTED;

		switch (connection.websocket.readyState) {
			case WebSocket.CONNECTING:
				return ConnectionState.CONNECTING;
			case WebSocket.OPEN:
				return ConnectionState.CONNECTED;
			case WebSocket.CLOSING:
			case WebSocket.CLOSED:
				return connection.retryCount > 0 ? ConnectionState.RECONNECTING : ConnectionState.DISCONNECTED;
			default:
				return ConnectionState.ERROR;
		}
	}

	/** Atualiza e emite mudança de estado da conexão */
	private updateConnectionState(viewId: symbol, newState: ConnectionState, reason?: string): void {
		const oldState = this.connectionStates.get(viewId) || ConnectionState.DISCONNECTED;
		if (oldState !== newState) {
			this.connectionStates.set(viewId, newState);
			this.emit<IConnectionStateChangeEvent>({
				type: EventType.CONNECTION_STATE_CHANGE,
				timestamp: performance.now(),
				viewId,
				oldState,
				newState,
				reason,
			});
		}
	}
}

// ===== CLASSE PRINCIPAL DO MUNDO DO JOGO =====
// Tipos para representar diferentes estados de câmera
interface CameraData {
	mass: number;
	scale: number;
	sumX: number;
	sumY: number;
	weight: number;
}

interface Camera {
	x: number;
	tx: number;
	y: number;
	ty: number;
	scale: number;
	tscale: number;
	merged: boolean;
	updated: number;
}

// Dados de visão para cada aba/visualização
interface Vision {
	border?: any;
	camera: Camera;
	leaderboard: any[];
	owned: Set<number>;
	spawned: number;
	stats?: any;
	used: number;
}

// Representa um frame de uma célula em um ponto no tempo
interface CellFrame {
	nx: number; // próxima posição X
	ny: number; // próxima posição Y
	nr: number; // próximo raio
	born: number; // momento de nascimento
	deadAt?: number; // momento da morte (se morreu)
	deadTo: number; // ID da célula que matou
	ox: number; // posição X anterior
	oy: number; // posição Y anterior
	or: number; // raio anterior
	jr: number; // raio para física jelly
	a: number; // alfa (opacidade)
	updated: number; // última atualização
}

// Registro de uma célula com histórico de frames
interface CellRecord {
	frames: CellFrame[];
}

// Representação completa de uma célula
interface Cell {
	views: Map<symbol, CellRecord>;
	model?: CellFrame;
	merged?: CellFrame;
}

// Coordenadas interpoladas de uma célula
interface CellPosition {
	x: number;
	y: number;
	r: number;
	jr: number;
	a: number;
}

class World {
	private static instance: World;

	// Armazenamento principal de entidades do jogo
	private readonly cells: Map<number, Cell> = new Map();
	private readonly pellets: Map<number, Cell> = new Map();

	// Símbolos únicos para identificar diferentes abas/visualizações
	private readonly multis: symbol[] = Array.from({ length: 8 }, () => Symbol());

	// IDs das visualizações principais
	private readonly viewId = {
		primary: this.multis[0],
		secondary: this.multis[1],
		spectate: Symbol(),
	};

	// Visualização atualmente selecionada
	private selected: symbol = this.viewId.primary;

	// Mapa de todas as visualizações ativas
	private readonly views: Map<symbol, Vision> = new Map();

	// Estado de sincronização entre abas
	private synchronized: boolean = false;
	private wasFlawlessSynchronized: boolean = false;
	private disagreementAt?: number;
	private disagreementStart?: number;
	private lastClean: number = performance.now();

	// Estatísticas do jogador
	private stats = {
		foodEaten: 0,
		highestPosition: 200,
		highestScore: 0,
		spawnedAt: undefined as number | undefined,
	};

	private readonly settings: typeof CONFIG.DEFAULT_SETTINGS & { cameraMovement: "default" | "instant" } = CONFIG.DEFAULT_SETTINGS;

	private constructor() {}

	// Padrão Singleton para garantir uma única instância
	static getInstance(): World {
		if (!World.instance) {
			World.instance = new World();
		}
		return World.instance;
	}

	// ===== MÉTODOS PÚBLICOS =====

	// Verifica se há células vivas em qualquer visualização
	isAlive(): boolean {
		for (const [view, vision] of this.views) {
			for (const id of vision.owned) {
				const cell = this.cells.get(id);
				// Se a célula não existe ainda, consideramos como viva
				if (!cell) return true;

				const frame = cell.views.get(view)?.frames[0];
				if (frame?.deadAt === undefined) return true;
			}
		}
		return false;
	}

	// Atualiza uma célula no mundo
	updateCell(cellId: number, cellData: ICellData, viewId: symbol, timestamp: number): void {
		let cell = this.cells.get(cellId);
		if (!cell) {
			cell = { views: new Map() };
			this.cells.set(cellId, cell);
		}

		let record = cell.views.get(viewId);
		if (!record) {
			record = { frames: [] };
			cell.views.set(viewId, record);
		}

		const frame: CellFrame = {
			nx: cellData.position.x,
			ny: cellData.position.y,
			nr: cellData.radius,
			born: timestamp,
			deadAt: cellData.isAlive ? undefined : timestamp,
			deadTo: 0,
			ox: cellData.position.x,
			oy: cellData.position.y,
			or: cellData.radius,
			jr: cellData.radius,
			a: cellData.isAlive ? 1 : 0,
			updated: timestamp,
		};

		// Adiciona o frame no início do array
		record.frames.unshift(frame);

		// Mantém apenas os frames mais recentes
		if (record.frames.length > 10) {
			record.frames = record.frames.slice(0, 10);
		}
	}

	// Atualiza um pellet no mundo
	updatePellet(pelletId: number, pelletData: ICellData, viewId: symbol, timestamp: number): void {
		let pellet = this.pellets.get(pelletId);
		if (!pellet) {
			pellet = { views: new Map() };
			this.pellets.set(pelletId, pellet);
		}

		let record = pellet.views.get(viewId);
		if (!record) {
			record = { frames: [] };
			pellet.views.set(viewId, record);
		}

		const frame: CellFrame = {
			nx: pelletData.position.x,
			ny: pelletData.position.y,
			nr: pelletData.radius,
			born: timestamp,
			deadAt: pelletData.isAlive ? undefined : timestamp,
			deadTo: 0,
			ox: pelletData.position.x,
			oy: pelletData.position.y,
			or: pelletData.radius,
			jr: pelletData.radius,
			a: pelletData.isAlive ? 1 : 0,
			updated: timestamp,
		};

		record.frames.unshift(frame);

		// Mantém apenas o frame mais recente para pellets
		if (record.frames.length > 1) {
			record.frames = record.frames.slice(0, 1);
		}
	}

	// Remove uma célula do mundo
	removeCell(cellId: number, viewId: symbol, timestamp: number): void {
		const cell = this.cells.get(cellId);
		if (cell) {
			const record = cell.views.get(viewId);
			if (record && record.frames.length > 0) {
				const lastFrame = record.frames[0];
				if (lastFrame.deadAt === undefined) {
					lastFrame.deadAt = timestamp;
					lastFrame.a = 0;
				}
			}
		}

		// Remove também dos pellets se existir
		const pellet = this.pellets.get(cellId);
		if (pellet) {
			const record = pellet.views.get(viewId);
			if (record && record.frames.length > 0) {
				const lastFrame = record.frames[0];
				if (lastFrame.deadAt === undefined) {
					lastFrame.deadAt = timestamp;
					lastFrame.a = 0;
				}
			}
		}
	}

	// Adiciona uma célula própria
	addOwnCell(cellId: number, viewId: symbol, timestamp: number): void {
		const vision = this.createVision(viewId);
		vision.owned.add(cellId);
		vision.spawned = timestamp;

		// Atualiza estatísticas
		if (this.stats.spawnedAt === undefined) {
			this.stats.spawnedAt = timestamp;
		}
	}

	// Remove uma célula própria
	removeOwnCell(cellId: number, viewId: symbol): void {
		const vision = this.views.get(viewId);
		if (vision) {
			vision.owned.delete(cellId);
		}
	}

	// Atualiza posição da câmera
	updateCameraPosition(viewId: symbol, x: number, y: number, scale: number, timestamp: number): void {
		const vision = this.createVision(viewId);
		vision.camera.tx = x;
		vision.camera.ty = y;
		vision.camera.tscale = scale;
		vision.camera.updated = timestamp;
		vision.used = timestamp;
	}

	// Atualiza leaderboard
	updateLeaderboard(viewId: symbol, leaderboard: any[], timestamp: number): void {
		const vision = this.createVision(viewId);
		vision.leaderboard = leaderboard;
		vision.used = timestamp;
	}

	// Atualiza limites do mapa
	updateMapBorders(viewId: symbol, left: number, top: number, right: number, bottom: number): void {
		const vision = this.createVision(viewId);
		vision.border = { left, top, right, bottom };
	}

	// Calcula a câmera para uma visualização específica
	calculateSingleCamera(view: symbol, vision?: Vision, weightExponent: number = 0, now: number = performance.now()): CameraData {
		vision = vision ?? this.views.get(view);
		if (!vision) {
			return { mass: 0, scale: 1, sumX: 0, sumY: 0, weight: 0 };
		}

		let mass = 0;
		let r = 0;
		let sumX = 0;
		let sumY = 0;
		let weight = 0;

		// Processa todas as células possuídas por esta visualização
		for (const id of vision.owned) {
			const cell = this.cells.get(id);
			const frame = this.synchronized ? cell?.merged : cell?.views.get(view)?.frames[0];
			const interp = this.synchronized ? cell?.merged : cell?.views.get(view)?.frames[0];

			if (!frame || !interp) continue;
			// Não incluir células possuídas antes do respawn
			if (frame.born < vision.spawned) continue;

			if (this.settings.cameraMovement === "instant") {
				const xyr = this.calculatePosition(frame, interp, undefined, undefined, false, now);
				r += xyr.r * xyr.a;
				mass += ((xyr.r * xyr.r) / 100) * xyr.a;
				const cellWeight = xyr.a * Math.pow(xyr.r, weightExponent);
				sumX += xyr.x * cellWeight;
				sumY += xyr.y * cellWeight;
				weight += cellWeight;
			} else {
				// Movimento de câmera padrão
				if (frame.deadAt !== undefined) continue;
				const xyr = this.calculatePosition(frame, interp, undefined, undefined, false, now);
				r += frame.nr;
				mass += (frame.nr * frame.nr) / 100;
				const cellWeight = Math.pow(frame.nr, weightExponent);
				sumX += xyr.x * cellWeight;
				sumY += xyr.y * cellWeight;
				weight += cellWeight;
			}
		}

		const scale = Math.pow(Math.min(64 / r, 1), 0.4);
		return { mass, scale, sumX, sumY, weight };
	}

	// Calcula e atualiza as posições de câmera para todas as visualizações
	updateCameras(now: number = performance.now()): void {
		const weightExponent = this.settings.camera !== "default" ? 2 : 0;

		// Cria conjuntos disjuntos de todas as câmeras próximas
		const cameras = new Map<symbol, CameraData>();
		const sets = new Map<symbol, Set<symbol>>();

		for (const [view, vision] of this.views) {
			cameras.set(view, this.calculateSingleCamera(view, vision, weightExponent, now));
			sets.set(view, new Set([view]));
		}

		// Calcula mesmo se as abas não forem realmente mescladas (para contornos multi)
		if (this.settings.multibox || this.settings.nbox) {
			this.mergeCameraSets(cameras, sets, now);
		}

		// Calcula e atualiza posições de câmera mescladas
		this.updateMergedCameras(cameras, sets, now);
	}

	// Cria ou retorna uma visualização existente
	createVision(view: symbol): Vision {
		const existing = this.views.get(view);
		if (existing) return existing;

		const vision: Vision = {
			border: undefined,
			camera: {
				x: 0,
				tx: 0,
				y: 0,
				ty: 0,
				scale: 0,
				tscale: 0,
				merged: false,
				updated: performance.now() - 1,
			},
			leaderboard: [],
			owned: new Set(),
			spawned: -Infinity,
			stats: undefined,
			used: -Infinity,
		};

		this.views.set(view, vision);
		return vision;
	}

	// Sincroniza frames entre diferentes visualizações
	synchronizeViews(): void {
		if (this.wasFlawlessSynchronized && this.settings.synchronization !== "flawless") this.cleanupFrameHistory();
		if (!this.settings.synchronization || this.views.size <= 1) {
			this.resetSynchronization();
			return;
		}

		const now = performance.now();
		const indices: Record<string | symbol, number> = {};

		if (this.settings.synchronization === "flawless") {
			this.performFlawlessSynchronization(indices, now);
		} else {
			this.performLatestSynchronization(indices);
		}

		this.mergeFrames(indices, now);
		this.cleanupFrameHistory();
		this.updateSynchronizationState(now);
	}

	calculateScore(view: symbol): number {
		let score = 0;
		const vision = this.views.get(view);
		if (!vision) return 0;

		for (const id of vision.owned) {
			const cell = this.cells.get(id);
			if (!cell) continue;

			const frame = this.synchronized ? cell.merged : cell.views.get(view)?.frames[0];
			if (!frame || frame.deadAt !== undefined) continue;

			// Usa pontuação exata do servidor, sem interpolação
			score += (frame.nr * frame.nr) / 100;
		}

		return score;
	}

	// Calcula posição interpolada de uma célula
	calculatePosition(
		frame: CellFrame,
		interp: CellFrame,
		killerFrame?: CellFrame,
		killerInterp?: CellFrame,
		isPellet: boolean = false,
		now: number = performance.now()
	): CellPosition {
		let nx = frame.nx;
		let ny = frame.ny;

		// Anima em direção à posição interpolada do assassino para suavidade extra
		if (killerFrame && killerInterp) {
			const killerXyr = this.calculatePosition(killerFrame, killerInterp, undefined, undefined, false, now);
			nx = killerXyr.x;
			ny = killerXyr.y;
		}

		let x: number, y: number, r: number, a: number;

		if (isPellet && frame.deadAt === undefined) {
			// Pellets não se movem suavemente
			x = nx;
			y = ny;
			r = frame.nr;
			a = 1;
		} else {
			// Interpolação suave para células
			let alpha = (now - interp.updated) / this.settings.drawDelay;
			alpha = Math.max(0, Math.min(1, alpha));

			x = interp.ox + (nx - interp.ox) * alpha;
			y = interp.oy + (ny - interp.oy) * alpha;
			r = interp.or + (frame.nr - interp.or) * alpha;

			const targetA = frame.deadAt !== undefined ? 0 : 1;
			a = interp.a + (targetA - interp.a) * alpha;
		}

		const dt = (now - interp.updated) / 1000;
		const jellyPhysicsSpeed = this.settings.slowerJellyPhysics ? 10 : 5;

		return {
			x,
			y,
			r,
			jr: this.exponentialEase(interp.jr, r, jellyPhysicsSpeed, dt),
			a,
		};
	}

	// Remove células mortas e invisíveis
	cleanupDeadCells(): void {
		const now = performance.now();
		if (now - this.lastClean < 200) return;
		this.lastClean = now;

		for (const collection of [this.cells, this.pellets]) {
			for (const [id, cell] of collection) {
				for (const [view, record] of cell.views) {
					const firstFrame = record.frames[0];
					const lastFrame = record.frames[record.frames.length - 1];

					if (firstFrame.deadAt !== lastFrame.deadAt) continue;
					if (lastFrame.deadAt !== undefined && now - lastFrame.deadAt >= this.settings.drawDelay + 200) {
						cell.views.delete(view);
					}
				}

				if (cell.views.size === 0) {
					collection.delete(id);
				}
			}
		}
	}

	// ===== MÉTODOS PRIVADOS =====

	// Mescla conjuntos de câmeras próximas
	private mergeCameraSets(cameras: Map<symbol, CameraData>, sets: Map<symbol, Set<symbol>>, now: number): void {
		for (const [view, vision] of this.views) {
			const set = sets.get(view)!;
			const camera = cameras.get(view)!;

			if (camera.weight <= 0 || now - vision.used > 20000) continue;

			const x = camera.sumX / camera.weight;
			const y = camera.sumY / camera.weight;
			const width = 1920 / 2 / camera.scale;
			const height = 1080 / 2 / camera.scale;

			for (const [otherView, otherVision] of this.views) {
				const otherSet = sets.get(otherView)!;
				if (set === otherSet || now - otherVision.used > 20000) continue;

				const otherCamera = cameras.get(otherView)!;
				if (otherCamera.weight <= 0) continue;

				const otherX = otherCamera.sumX / otherCamera.weight;
				const otherY = otherCamera.sumY / otherCamera.weight;
				const otherWidth = 1920 / 2 / otherCamera.scale;
				const otherHeight = 1080 / 2 / otherCamera.scale;

				// Limite de proximidade baseado na massa de ambas as abas
				const threshold = 1000 + Math.min(camera.weight / 100 / 25, otherCamera.weight / 100 / 25);

				if (Math.abs(x - otherX) <= width + otherWidth + threshold && Math.abs(y - otherY) <= height + otherHeight + threshold) {
					// Mesclar conjuntos disjuntos
					for (const connectedView of otherSet) {
						set.add(connectedView);
						sets.set(connectedView, set);
					}
				}
			}
		}
	}

	// Atualiza câmeras mescladas
	private updateMergedCameras(cameras: Map<symbol, CameraData>, sets: Map<symbol, Set<symbol>>, now: number): void {
		const computed = new Set<Set<symbol>>();

		for (const set of sets.values()) {
			if (computed.has(set)) continue;

			let mass = 0;
			let sumX = 0;
			let sumY = 0;
			let weight = 0;

			if (this.settings.mergeCamera) {
				for (const view of set) {
					const camera = cameras.get(view)!;
					mass += camera.mass;
					sumX += camera.sumX;
					sumY += camera.sumY;
					weight += camera.weight;
				}
			}

			for (const view of set) {
				const vision = this.views.get(view)!;

				if (!this.settings.mergeCamera) {
					const camera = cameras.get(view)!;
					({ mass, sumX, sumY, weight } = camera);
				}

				this.updateSingleCameraPosition(vision, mass, sumX, sumY, weight, set.size > 1, now);
			}

			computed.add(set);
		}
	}

	// Atualiza posição de uma única câmera
	private updateSingleCameraPosition(
		vision: Vision,
		mass: number,
		sumX: number,
		sumY: number,
		weight: number,
		isMerged: boolean,
		now: number
	): void {
		let xyFactor: number;

		if (weight <= 0) {
			xyFactor = 20;
		} else if (this.settings.cameraMovement === "instant") {
			xyFactor = 1;
		} else {
			// Movimento de câmera suave após spawnar
			const aliveFor = (performance.now() - vision.spawned) / 1000;
			const a = Math.max(0, Math.min(1, (aliveFor - 0.3) / 0.3));
			const base = this.settings.cameraSpawnAnimation ? 2 : 1;
			xyFactor = Math.min(this.settings.cameraSmoothness, base * (1 - a) + this.settings.cameraSmoothness * a);
		}

		if (weight > 0) {
			vision.camera.tx = sumX / weight;
			vision.camera.ty = sumY / weight;

			let scale: number;
			if (this.settings.camera === "default") {
				scale = Math.pow(Math.min(64 / Math.sqrt(mass), 1), 0.4);
			} else {
				scale = Math.pow(Math.min(64 / Math.sqrt(100 * mass), 1), 0.4);
			}
			vision.camera.tscale = this.settings.autoZoom ? scale : 0.25;
		}

		const dt = (now - vision.camera.updated) / 1000;
		vision.camera.x = this.exponentialEase(vision.camera.x, vision.camera.tx, xyFactor, dt);
		vision.camera.y = this.exponentialEase(vision.camera.y, vision.camera.ty, xyFactor, dt);
		vision.camera.scale = this.exponentialEase(vision.camera.scale, vision.camera.tscale, 9, dt);

		vision.camera.merged = isMerged;
		vision.camera.updated = now;
	}

	// Implementa sincronização perfeita entre visualizações (versão simplificada)
	private performFlawlessSynchronization(indices: Record<string | symbol, number>, now: number): void {
		// Versão simplificada da sincronização perfeita
		// Em uma implementação real, isso envolveria algoritmos complexos de grafos bipartidos
		let i = 0;
		for (const view of this.views.keys()) {
			indices[i++] = indices[view] = 0; // Por simplicidade, usar sempre índice 0
		}
		this.wasFlawlessSynchronized = true;
	}

	// Implementa sincronização usando frames mais recentes
	private performLatestSynchronization(indices: Record<string | symbol, number>): void {
		let i = 0;
		for (const view of this.views.keys()) {
			indices[i++] = indices[view] = 0;
		}
		this.wasFlawlessSynchronized = false;
	}

	// Mescla frames baseado nos índices encontrados
	private mergeFrames(indices: Record<string | symbol, number>, now: number): void {
		for (const collection of [this.cells, this.pellets]) {
			for (const cell of collection.values()) {
				// Encontra frame modelo (versão simplificada)
				let modelFrame: CellFrame | undefined;

				for (const [view, record] of cell.views) {
					const frame = record.frames[indices[view] || 0];
					if (frame && !modelFrame) {
						modelFrame = frame;
					}
				}

				if (modelFrame) {
					cell.model = modelFrame;
				}
			}
		}

		// Atualiza frames mesclados
		this.updateMergedFrames(now);
	}

	// Atualiza frames mesclados para todas as células
	private updateMergedFrames(now: number): void {
		for (const collection of [this.cells, this.pellets]) {
			for (const cell of collection.values()) {
				const { model, merged } = cell;
				if (!model) {
					cell.merged = undefined;
					continue;
				}

				if (!merged || (merged.deadAt !== undefined && model.deadAt === undefined)) {
					// Cria novo frame mesclado
					cell.merged = {
						nx: model.nx,
						ny: model.ny,
						nr: model.nr,
						born: now,
						deadAt: model.deadAt !== undefined ? now : undefined,
						deadTo: model.deadTo,
						ox: model.nx,
						oy: model.ny,
						or: model.nr,
						jr: model.nr,
						a: 0,
						updated: now,
					};
				} else {
					// Atualiza frame mesclado existente
					if (
						merged.deadAt === undefined &&
						(model.deadAt !== undefined || model.nx !== merged.nx || model.ny !== merged.ny || model.nr !== merged.nr)
					) {
						const isPellet = collection === this.pellets;
						const xyr = this.calculatePosition(merged, merged, undefined, undefined, isPellet, now);

						merged.ox = xyr.x;
						merged.oy = xyr.y;
						merged.or = xyr.r;
						merged.jr = xyr.jr;
						merged.a = xyr.a;
						merged.updated = now;
					}

					merged.nx = model.nx;
					merged.ny = model.ny;
					merged.nr = model.nr;
					merged.deadAt = model.deadAt !== undefined ? merged.deadAt ?? now : undefined;
					merged.deadTo = model.deadTo;
				}
			}
		}
	}

	// Remove histórico de frames
	private cleanupFrameHistory(): void {
		for (const collection of [this.cells, this.pellets]) {
			for (const cell of collection.values()) {
				for (const record of cell.views.values()) {
					// Remove frames antigos, mantendo apenas o atual
					while (record.frames.length > 1) {
						record.frames.pop();
					}
				}
			}
		}
	}

	// Reset do estado de sincronização
	private resetSynchronization(): void {
		this.disagreementStart = undefined;
		this.disagreementAt = undefined;
		this.synchronized = false;
		this.wasFlawlessSynchronized = false;
	}

	// Atualiza estado de sincronização
	private updateSynchronizationState(now: number): void {
		this.disagreementStart = undefined;

		// Se houve desacordo, espera um tempo antes de reativar sincronização
		if (this.disagreementAt === undefined || now - this.disagreementAt > 1000) {
			this.synchronized = true;
		}
	}

	// Utilitário para easing exponencial
	private exponentialEase(current: number, target: number, factor: number, deltaTime: number): number {
		return current + (target - current) * (1 - Math.exp(-factor * deltaTime));
	}

	// ===== GETTERS PÚBLICOS =====

	get gameStats() {
		return { ...this.stats };
	}

	get currentView(): symbol {
		return this.selected;
	}

	get allViews(): ReadonlyMap<symbol, Vision> {
		return this.views;
	}

	get isSynchronized(): boolean {
		return this.synchronized;
	}

	get cellCount(): number {
		return this.cells.size;
	}

	get pelletCount(): number {
		return this.pellets.size;
	}

	// Obtém estatísticas detalhadas do mundo
	getDetailedStats(): {
		cells: number;
		pellets: number;
		views: number;
		ownedCells: number;
		totalScore: number;
		averageLatency: number;
		isAlive: boolean;
		isSynchronized: boolean;
		uptime: number;
	} {
		let totalOwnedCells = 0;
		let totalScore = 0;
		let totalLatency = 0;
		let latencyCount = 0;

		for (const [viewId, vision] of this.views) {
			totalOwnedCells += vision.owned.size;
			totalScore += this.calculateScore(viewId);

			if (vision.stats?.latency) {
				totalLatency += vision.stats.latency;
				latencyCount++;
			}
		}

		return {
			cells: this.cellCount,
			pellets: this.pelletCount,
			views: this.views.size,
			ownedCells: totalOwnedCells,
			totalScore,
			averageLatency: latencyCount > 0 ? totalLatency / latencyCount : 0,
			isAlive: this.isAlive(),
			isSynchronized: this.isSynchronized,
			uptime: performance.now() - (this.stats.spawnedAt || performance.now()),
		};
	}

	// Obtém informações de uma visualização específica
	getViewInfo(viewId: symbol): {
		ownedCells: number[];
		score: number;
		camera: Camera;
		leaderboard: any[];
		stats?: any;
		isActive: boolean;
	} | null {
		const vision = this.views.get(viewId);
		if (!vision) return null;

		return {
			ownedCells: Array.from(vision.owned),
			score: this.calculateScore(viewId),
			camera: { ...vision.camera },
			leaderboard: [...vision.leaderboard],
			stats: vision.stats ? { ...vision.stats } : undefined,
			isActive: performance.now() - vision.used < 5000, // Ativa se usada nos últimos 5 segundos
		};
	}
}

// ===== GERENCIADOR DE UI =====
interface IUIComponent {
	render(): HTMLElement;
	destroy(): void;
	isVisible(): boolean;
	show(): void;
	hide(): void;
}

interface IUIManager {
	initialize(): boolean;
	destroy(): void;
	showDebugPanel(): void;
	hideDebugPanel(): void;
	showHelpDialog(): void;
	registerComponent(name: string, component: IUIComponent): void;
	getComponent(name: string): IUIComponent | undefined;
}

class UIManager implements IUIManager {
	private static instance: UIManager;
	private readonly keyBindManager: KeyBindManager;
	private readonly components = new Map<string, IUIComponent>();
	private isInitialized = false;
	private debugPanelVisible = false;

	private constructor() {
		this.keyBindManager = KeyBindManager.getInstance();
	}

	static getInstance(): UIManager {
		if (!UIManager.instance) {
			UIManager.instance = new UIManager();
		}
		return UIManager.instance;
	}

	initialize(): boolean {
		if (this.isInitialized) return true;

		this.setupKeyBindings();
		this.createDebugPanel();
		this.isInitialized = true;
		return true;
	}

	private setupKeyBindings(): void {
		this.keyBindManager.register({
			key: "F1",
			handler: () => this.showHelpDialog(),
			description: "Mostra a ajuda do script",
		});

		this.keyBindManager.register({
			key: "F2",
			handler: () => this.showDebugPanel(),
			description: "Mostra/oculta painel de debug",
		});

		this.keyBindManager.register({
			key: "F3",
			handler: () => {
				const app = ScriptApplication.getInstance();
				const status = app.getStatus();
				console.log("📊 Status da aplicação:", status);
			},
			description: "Mostra status da aplicação",
		});

		this.keyBindManager.register({
			key: "F4",
			handler: () => {
				const world = World.getInstance();
				const detailedStats = world.getDetailedStats();

				console.group("🌍 Estado Detalhado do Mundo");
				console.log("📊 Estatísticas Gerais:", detailedStats);

				// Mostra informações de cada visualização
				for (const [viewId, _] of world.allViews) {
					const viewInfo = world.getViewInfo(viewId);
					if (viewInfo) {
						console.log(`👁️ Visualização ${viewId.toString()}:`, viewInfo);
					}
				}
				console.groupEnd();
			},
			description: "Mostra estado detalhado do mundo",
		});
	}

	private createDebugPanel(): void {
		// Implementação básica do painel de debug
		// Em uma implementação completa, isso seria um componente mais elaborado
	}

	showDebugPanel(): void {
		if (this.debugPanelVisible) {
			this.hideDebugPanel();
			return;
		}

		const interceptor = WebSocketInterceptor.getInstance();
		const info = interceptor.getAllConnectionsInfo();
		const stats = interceptor.getNetworkStats();
		const recent = interceptor.getRecentMessages(30);

		console.group("🔍 Painel de Debug");
		console.log("  Conexões:", info);
		console.log("📊 Estatísticas:", stats);
		console.log("🕒 Mensagens recentes:", recent);
		console.groupEnd();

		this.debugPanelVisible = true;
	}

	hideDebugPanel(): void {
		this.debugPanelVisible = false;
		console.log("🔍 Painel de debug ocultado");
	}

	showHelpDialog(): void {
		const helpText = `
🎮 ${CONFIG.SCRIPT_NAME} v${CONFIG.VERSION}

⌨️  Atalhos de Teclado:
F1 - Mostra esta ajuda
F2 - Mostra/oculta painel de debug
F3 - Mostra status da aplicação
F4 - Mostra estado do mundo

🔧 Funcionalidades:
• Interceptação de WebSocket em tempo real
• Sistema de eventos para atualizações do jogo
• Gerenciamento de estado do mundo
• Sincronização entre múltiplas abas
• Monitoramento de latência e conexão

📝 Configurações são salvas automaticamente no localStorage.
		`;

		alert(helpText.trim());
	}

	registerComponent(name: string, component: IUIComponent): void {
		this.components.set(name, component);
	}

	getComponent(name: string): IUIComponent | undefined {
		return this.components.get(name);
	}

	destroy(): void {
		this.components.forEach(component => component.destroy());
		this.components.clear();
		this.isInitialized = false;
	}
}

// ===== APLICAÇÃO PRINCIPAL =====
interface IScriptApplication {
	initialize(): Promise<boolean>;
	destroy(): Promise<void>;
	getStatus(): IApplicationStatus;
}

interface IApplicationStatus {
	readonly isInitialized: boolean;
	readonly version: string;
	readonly uptime: number;
	readonly connectionCount: number;
	readonly lastError?: string;
}

class ScriptApplication implements IScriptApplication {
	private static instance: ScriptApplication;
	private readonly settingsStore: SettingsStore;
	private readonly uiManager: UIManager;
	private readonly wsInterceptor: WebSocketInterceptor;
	private readonly world: World;
	private readonly startTime = performance.now();
	private isInitialized = false;
	private lastError?: string;

	private constructor() {
		this.settingsStore = SettingsStore.getInstance();
		this.uiManager = UIManager.getInstance();
		this.wsInterceptor = WebSocketInterceptor.getInstance();
		this.world = World.getInstance();
		this.setupEventListeners();
	}

	static getInstance(): ScriptApplication {
		if (!ScriptApplication.instance) {
			ScriptApplication.instance = new ScriptApplication();
		}
		return ScriptApplication.instance;
	}

	private setupEventListeners(): void {
		// Escuta eventos de conexão
		this.wsInterceptor.on(EventType.CONNECTION_STATE_CHANGE, (event: IConnectionStateChangeEvent) => {
			console.log(`🔄 Estado da conexão mudou: ${event.oldState} → ${event.newState}`, event.reason || "");
		});

		// Escuta eventos de atualização do mundo
		this.wsInterceptor.on(EventType.WORLD_UPDATE, () => {
			this.world.updateCameras();
			this.world.synchronizeViews();
			this.world.cleanupDeadCells();
		});

		// Escuta eventos de posição
		this.wsInterceptor.on(EventType.POSITION_UPDATE, (event: IPositionUpdateEvent) => {
			// Atualiza posição da câmera no mundo
			if (CONFIG.DEFAULT_SETTINGS.enableDebugMode) {
				console.log("📍 Posição atualizada:", event.position, "Escala:", event.scale);
			}
		});

		// Escuta eventos de spawn de células
		this.wsInterceptor.on(EventType.CELL_SPAWN, (event: ICellSpawnEvent) => {
			if (CONFIG.DEFAULT_SETTINGS.enableDebugMode) {
				console.log("🆕 Nova célula:", event.cellId);
			}
		});

		// Escuta mudanças de estado de conexão
		this.wsInterceptor.on(EventType.CONNECTION_STATE_CHANGE, (event: IConnectionStateChangeEvent) => {
			if (event.newState === ConnectionState.CONNECTED) {
				console.log("✅ Conectado ao servidor");
			} else if (event.newState === ConnectionState.DISCONNECTED) {
				console.log("❌ Desconectado do servidor");
			}
		});
	}

	async initialize(): Promise<boolean> {
		try {
			if (this.isInitialized) return true;

			// Aguarda o DOM estar pronto
			if (document.readyState === "loading") {
				await new Promise(resolve => document.addEventListener("DOMContentLoaded", resolve));
			}

			// Inicializa componentes
			this.uiManager.initialize();
			this.settingsStore.setSetting("appVersion", CONFIG.VERSION);
			this.settingsStore.setSetting("lastInitialized", Date.now());

			// Log de inicialização
			console.log(`🚀 ${CONFIG.SCRIPT_NAME} v${CONFIG.VERSION} inicializado`);
			console.log("ℹ️  Pressione F1 para ajuda, F2 para status do WebSocket");
			console.log("🔧 Configurações:", this.settingsStore.getAllSettings());

			this.isInitialized = true;
			return true;
		} catch (error) {
			this.lastError = error instanceof Error ? error.message : String(error);
			console.error("❌ Erro na inicialização:", error);
			return false;
		}
	}

	async destroy(): Promise<void> {
		try {
			this.wsInterceptor.removeAllListeners();
			this.uiManager.destroy();
			this.isInitialized = false;
			console.log("🛑 Aplicação finalizada");
		} catch (error) {
			console.error("❌ Erro ao finalizar aplicação:", error);
		}
	}

	getStatus(): IApplicationStatus {
		return {
			isInitialized: this.isInitialized,
			version: CONFIG.VERSION,
			uptime: performance.now() - this.startTime,
			connectionCount: this.wsInterceptor.getAllConnectionsInfo().length,
			lastError: this.lastError,
		};
	}
}

// ===== INICIALIZAÇÃO AUTOMÁTICA =====
(async () => {
	try {
		const app = ScriptApplication.getInstance();
		await app.initialize();

		// Adiciona handler global para erros não capturados
		window.addEventListener("error", event => {
			console.error("❌ Erro global capturado:", event.error);
		});

		// Adiciona handler para promises rejeitadas
		window.addEventListener("unhandledrejection", event => {
			console.error("❌ Promise rejeitada:", event.reason);
		});
	} catch (error) {
		console.error("❌ Falha crítica na inicialização:", error);
	}
})();
